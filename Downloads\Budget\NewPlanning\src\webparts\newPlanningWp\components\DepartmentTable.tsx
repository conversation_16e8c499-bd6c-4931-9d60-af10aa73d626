import React from "react";

interface MonthTotals {
  op?: string;
  ea1?: string;
  ea2?: string;
  yearEnd?: string;
  total?: string;
}

interface Row {
  spendingLevel: string;
  months: string[];
  totals: MonthTotals;
}

interface YearData {
  year: number;
  rows: Row[];
}

interface DepartmentTableProps {
  index: number;
  tblCounter: number;
  curYear: number;
  createDepartmentTable: () => void;
  deleteSelectedTables: () => void;
  selectedYears: number[];
  toggleYearSelection: (year: number) => void;
  tableYears: number[];
  rowData: YearData[];
  spendingLevels: string[];
  editableLevels: Set<string>;
  handleMonthChange: (
    yearIdx: number,
    rowIdx: number,
    monthIdx: number,
    value: string
  ) => void;
}

const DepartmentTable: React.FC<DepartmentTableProps> = ({
  index,
  tblCounter,
  curYear,
  createDepartmentTable,
  deleteSelectedTables,
  selectedYears,
  toggleYearSelection,
  tableYears,
  rowData,
  spendingLevels,
  editableLevels,
  handleMonthChange,
}) => {
  return (
    <div>
      {tableYears.map((year, idx) => (
        <div key={year} style={{ marginBottom: 40, padding: 20 }}>
          {idx !== 0 && (
            <input
              type="checkbox"
              checked={selectedYears.includes(year)}
              onChange={() => toggleYearSelection(year)}
            />
          )}
          <h3>Department Table - Year {year}</h3>
          <div style={{ overflowX: "auto", width: "100%" }}>
            <table
              style={{
                tableLayout: "auto",
                width: "auto",
                borderCollapse: "collapse",
              }}
            >
              <thead>
                <tr>
                  <th
                    style={{
                      padding: "8px",
                      border: "1px solid #ccc",
                      whiteSpace: "nowrap",
                    }}
                  >
                    Year
                  </th>
                <th
  style={{
    padding: "8px",
    border: "1px solid #ccc",
    textAlign: "right",
    whiteSpace: "nowrap",
  }}
              >
                Total Year Plan 
              </th>
                </tr>
                <tr>
                  <td
                    style={{
                      padding: "8px",
                      border: "1px solid #ccc",
                      whiteSpace: "nowrap",
                    }}
                  >
                    <select defaultValue={year} style={{ width: "100%" }}>
                      {Array.from({ length: 2036 - curYear }, (_, i) => curYear + i).map(
                        (y) => (
                          <option key={y} value={y}>
                            {y}
                          </option>
                        )
                      )}
                    </select>
                  </td>
                  <td
                    style={{
                      padding: "8px",
                      border: "1px solid #ccc",
                      whiteSpace: "nowrap",
                    }}
                  >
                     {
                      rowData
                        .find((r) => r.year === year)
                        ?.rows.find((r) => r.spendingLevel === "SL4 Payment Plan")
                        ?.totals.total
                    }
                   
                  </td>
                </tr>
                <tr>
                  <th>Year</th>
                  <th>Spending Level</th>
                  {[
                    "JAN",
                    "FEB",
                    "MAR",
                    "APR",
                    "MAY",
                    "JUN",
                    "JUL",
                    "AUG",
                    "SEP",
                    "OCT",
                    "NOV",
                    "DEC",
                  ].map((month) => (
                    <th key={month}>{month}</th>
                  ))}
                  <th>OP</th>
                  <th>EA1</th>
                  <th>EA2</th>
                  <th>Year-End</th>
                </tr>
              </thead>
              <tbody>
                {spendingLevels.map((level, i) => {
                  const yearData = rowData.find((r) => r.year === year);
                  const row = yearData?.rows.find((r) => r.spendingLevel === level);

                  return (
                    <tr key={i}>
                      <td
                        style={{
                          whiteSpace: "nowrap",
                          border: "1px solid #ddd",
                          padding: "8px",
                        }}
                      >
                        <select defaultValue={year}>
                          {Array.from({ length: 2036 - curYear }, (_, i) => curYear + i).map(
                            (y) => (
                              <option key={y} value={y}>
                                {y}
                              </option>
                            )
                          )}
                        </select>
                      </td>
                      <td
                        style={{
                          whiteSpace: "nowrap",
                          border: "1px solid #ddd",
                          padding: "8px",
                        }}
                      >
                        {level}
                      </td>
                      {Array.from({ length: 12 }).map((_, monthIdx) => (
                        <td
                          key={monthIdx}
                          style={{
                            whiteSpace: "nowrap",
                            border: "1px solid #ddd",
                            padding: "8px",
                          }}
                        >
                     <input
                        type="text"
                        disabled={!editableLevels.has(level)}
                        style={{ width: "100px" }}
                        value={row?.months[monthIdx] ?? ""}
                        onChange={(e) =>
                            handleMonthChange(idx, i, monthIdx, e.target.value)
                        }
                        />
                        </td>
                      ))}
                      <td>
                        <input
                          type="text"
                          disabled
                          style={{ width: "100px" }}
                          value={row?.totals.op || ""}

                        />
                      </td>
                      <td>
                        <input
                          type="text"
                          disabled
                          style={{ width: "100px" }}
                            value={row?.totals.ea1 || ""}

                        />
                      </td>
                      <td>
                        <input
                          type="text"
                          disabled
                          style={{ width: "100px" }}
                            value={row?.totals.ea2 || ""}
                        />
                      </td>
                      <td>
                        <input
                          type="text"
                          disabled
                          style={{ width: "100px" }}
                          value={row?.totals.yearEnd}
                        />
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      ))}

      <div style={{ textAlign: "right", marginTop: 20 }}>
        <button
          onClick={createDepartmentTable}
          style={{
            backgroundColor: "green",
            color: "white",
            border: "none",
            padding: "8px 16px",
            cursor: "pointer",
            marginRight: 10,
          }}
        >
          Add
        </button>

        <button
          onClick={deleteSelectedTables}
          style={{
            backgroundColor: "#d9534f",
            color: "white",
            border: "none",
            padding: "8px 16px",
            cursor: selectedYears.length === 0 ? "not-allowed" : "pointer",
            opacity: selectedYears.length === 0 ? 0.6 : 1,
          }}
          disabled={selectedYears.length === 0}
        >
          Delete
        </button>
      </div>
    </div>
  );
};

export default DepartmentTable;
