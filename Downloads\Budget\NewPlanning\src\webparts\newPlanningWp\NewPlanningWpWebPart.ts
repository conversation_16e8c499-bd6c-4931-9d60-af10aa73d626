import * as React from 'react';
import * as ReactDom from 'react-dom';
import { Version } from '@microsoft/sp-core-library';
import {
  type IPropertyPaneConfiguration,
  PropertyPaneTextField
} from '@microsoft/sp-property-pane';
import { BaseClientSideWebPart } from '@microsoft/sp-webpart-base';
import { IReadonlyTheme } from '@microsoft/sp-component-base';

import * as strings from 'NewPlanningWpWebPartStrings';
import NewPlanningWp from './components/NewPlanningWp';
import { INewPlanningWpProps } from './components/INewPlanningWpProps';

export interface INewPlanningWpWebPartProps {
  description: string;
}

export default class NewPlanningWpWebPart extends BaseClientSideWebPart<INewPlanningWpWebPartProps> {

  private _isDarkTheme: boolean = false;
  private _environmentMessage: string = '';

  public render(): void {
    const element: React.ReactElement<INewPlanningWpProps> = React.createElement(
      NewPlanningWp,
      {
        description: this.properties.description,
        isDarkTheme: this._isDarkTheme,
        environmentMessage: this._environmentMessage,
        hasTeamsContext: !!this.context.sdks.microsoftTeams,
        userDisplayName: this.context.pageContext.user.displayName,
        context:this.context,
    
      }
    );

    ReactDom.render(element, this.domElement);
  }

  protected onInit(): Promise<void> {
    return this._getEnvironmentMessage().then(message => {
      this._environmentMessage = message;
      const style = document.createElement("style");
    style.innerHTML = `
      @media screen and (min-width: 1024px) {
        .c_b_cb6f7c2e:not(.g_b_cb6f7c2e) .j_b_cb6f7c2e {
          display: block !important;
          max-width: 1600px !important;
        }
      }

      .CanvasZone {
        padding: 0 !important;
        max-width: 1600px !important;
        margin: 0 auto !important;
      }

      .CanvasSection {
        padding: 0 !important;
      }

      .ControlZone .ControlZone--clean .a_a_50a7110f {
        margin: 0 !important;
        padding: 0 !important;
      }

      #workbenchPageContent {
        max-width: none !important;
        width: 100% !important;
      }

      #sp-appBar,
      #spCommandBar,
      #SuiteNavWrapper,
      .commandBarButtonHeightAndColor[aria-label="Command bar"],
      .headerRow-50,
      .j_m_4ade22aa#CommentsWrapper {
        display: none !important;
      }

      .p_e_8474018e {
        padding: 0 !important;
      }

      .a_g_cb6f7c2e:not(.e_g_cb6f7c2e):not(.aa_g_cb6f7c2e) {
        margin: 0 !important;
        padding: 0 !important;
      }

      @media screen and (min-width: 1024px) {
        .a_a_cb6f7c2e:not(.e_a_cb6f7c2e) .h_a_cb6f7c2e {
          max-width: 100% !important;
        }
      }
        .m_b_d71df89c {
        overflow-y: hidden !important;
      }
    `;
    // Defer style append to ensure DOM elements exist
      document.head.appendChild(style);
    });
  }



  private _getEnvironmentMessage(): Promise<string> {
    if (!!this.context.sdks.microsoftTeams) { // running in Teams, office.com or Outlook
      return this.context.sdks.microsoftTeams.teamsJs.app.getContext()
        .then(context => {
          let environmentMessage: string = '';
          switch (context.app.host.name) {
            case 'Office': // running in Office
              environmentMessage = this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentOffice : strings.AppOfficeEnvironment;
              break;
            case 'Outlook': // running in Outlook
              environmentMessage = this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentOutlook : strings.AppOutlookEnvironment;
              break;
            case 'Teams': // running in Teams
            case 'TeamsModern':
              environmentMessage = this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentTeams : strings.AppTeamsTabEnvironment;
              break;
            default:
              environmentMessage = strings.UnknownEnvironment;
          }

          return environmentMessage;
        });
    }

    return Promise.resolve(this.context.isServedFromLocalhost ? strings.AppLocalEnvironmentSharePoint : strings.AppSharePointEnvironment);
  }

  protected onThemeChanged(currentTheme: IReadonlyTheme | undefined): void {
    if (!currentTheme) {
      return;
    }

    this._isDarkTheme = !!currentTheme.isInverted;
    const {
      semanticColors
    } = currentTheme;

    if (semanticColors) {
      this.domElement.style.setProperty('--bodyText', semanticColors.bodyText || null);
      this.domElement.style.setProperty('--link', semanticColors.link || null);
      this.domElement.style.setProperty('--linkHovered', semanticColors.linkHovered || null);
    }

  }

  protected onDispose(): void {
    ReactDom.unmountComponentAtNode(this.domElement);
  }

  protected get dataVersion(): Version {
    return Version.parse('1.0');
  }

  protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {
    return {
      pages: [
        {
          header: {
            description: strings.PropertyPaneDescription
          },
          groups: [
            {
              groupName: strings.BasicGroupName,
              groupFields: [
                PropertyPaneTextField('description', {
                  label: strings.DescriptionFieldLabel
                })
              ]
            }
          ]
        }
      ]
    };
  }
}
