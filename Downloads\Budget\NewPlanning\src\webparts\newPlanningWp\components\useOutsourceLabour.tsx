import React from "react";
import { RowData } from './RenderOutsourceTable'


type UseOutsourceLabourReturn = {
  outsourceYears: number[];
  selectedOutsourceYears: number[];
  outsourceRowData: RowData[][];
  addOutsourceTable: () => void;
  deleteSelectedOutsourceTables: () => void;
  toggleOutsourceYear: (year: number) => void;
  handleOutsourceMonthChange: (yearIdx: number, rowIdx: number, monthIdx: number, value: string) => void;
};
type YearSummary = {
  op: number;
  ea1: number;
  ea2: number;
  yearEnd: number;
  total: number;
};

  const formatMoney = (value: number): string => {
  if (value === 0) return "";  // Optional: don't show 0 values
  return value.toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

  const useOutsourceLabour = (curYear: number): UseOutsourceLabourReturn => {
  const [outsourceYears, setOutsourceYears] = React.useState<number[]>([curYear]);
  const [selectedOutsourceYears, setSelectedOutsourceYears] = React.useState<number[]>([]);
const [outsourceRowData, setOutsourceRowData] = React.useState<RowData[][]>(() => [
  [
    {
      category: "FTE Plan (Numbers)",
      months: Array(12).fill("") as string[],  // <-- cast here
      totals: { op: "", ea1: "", ea2: "", yearEnd: "" },
      yearSummary: { plan: 0, fte: 0 },
    },
    {
      category: "FTE Budget Plan (T-JPY)",
      months: Array(12).fill("") as string[],
      totals: { op: "", ea1: "", ea2: "", yearEnd: "" },
      yearSummary: { plan: 0, fte: 0 },
    },
    {
      category: "FTE Budget Actual (T-JPY)",
      months: Array(12).fill("") as string[],
      totals: { op: "", ea1: "", ea2: "", yearEnd: "" },
      yearSummary: { plan: 0, fte: 0 },
    },
  ],
]);

  // Placeholder for your actual summary calculation logic
const calculateYearSummary = (
  monthlyValues: number[],
  spendingLevel: string,   // e.g. "Labour"
  year: number,
  planValue?: string       // optional parameter if needed
):YearSummary => {
  const now = new Date();
  const currentYear = now.getFullYear();
  const curMonth = now.getMonth(); // 0 = Jan, 11 = Dec

  const total = monthlyValues.reduce((sum, val) => sum + (isNaN(val) ? 0 : val), 0);

  let op = 0,
      ea1 = 0,
      ea2 = 0,
      yearEnd = 0;

  if (year === currentYear) {
    if (curMonth >= 0 && curMonth <= 2) {
      // Jan-Mar: OP = full year total
      op = total;
    } else if (curMonth >= 3 && curMonth <= 5) {
      // Apr-Jun: EA1 = full year total
      ea1 = total;
    } else if (curMonth >= 6 && curMonth <= 8) {
      // Jul-Sep: EA2 = full year total
      ea2 = total;
    } else if (curMonth >= 9 && curMonth <= 11) {
      // Oct-Dec: Year-End = full year total
      yearEnd = total;
    }
  } else {
    // For past or future years, assign OP to total as default
    op = total;
  }

  return {
    op,
    ea1,
    ea2,
    yearEnd,
    total,
  };
};

const addOutsourceTable = (): void => {
  const lastYear = outsourceYears[outsourceYears.length - 1];
  if (lastYear >= 2035) return;

  const newYear = lastYear + 1;

  const newRows: RowData[] = [
    "FTE Plan (Numbers)",
    "FTE Budget Plan (T-JPY)",
    "FTE Budget Actual (T-JPY)",
  ].map((category) => {
    const months = Array(12).fill("");
    const monthNumbers = months.map((m) => parseFloat(m) || 0);
    const { op, ea1, ea2, yearEnd, total } = calculateYearSummary(monthNumbers, "Outsource", newYear);

    const conversionRates: { [year: number]: number } = {
      2024: 19.2,
      2025: 19.2,
      2026: 19.2,
    };
    const conversionRate = conversionRates[newYear] || 1;
    const fte = conversionRate > 0 ? total / conversionRate : 0;

    return {
      category,
      months,
      totals: {
        op: op ? formatMoney(op) : "",
        ea1: ea1 ? formatMoney(ea1) : "",
        ea2: ea2 ? formatMoney(ea2) : "",
        yearEnd: yearEnd ? formatMoney(yearEnd) : "",
      },
      yearSummary: {
        plan: total,
        fte,
      },
    };
  });

  setOutsourceYears((prevYears) => [...prevYears, newYear]);
  setOutsourceRowData((prevData) => [...prevData, newRows]);
};

  const deleteSelectedOutsourceTables = () :void=> {
    setOutsourceYears(prev => prev.filter(year => !selectedOutsourceYears.includes(year)));
   setOutsourceRowData(prev => {
  return prev.filter((_, idx) => {
    const year = outsourceYears[idx];
    return !selectedOutsourceYears.includes(year);
  });
});
    setSelectedOutsourceYears([]);
  };

  const toggleOutsourceYear = (year: number):void => {
    setSelectedOutsourceYears(prev =>
      prev.includes(year) ? prev.filter(y => y !== year) : [...prev, year]
    );
  };




  // Format money as needed

  const handleOutsourceMonthChange = (yearIdx: number, rowIdx: number, monthIdx: number, value: string):void => {
    setOutsourceRowData(prev => {
      const newData = [...prev];
      if (!newData[yearIdx] || !newData[yearIdx][rowIdx]) return prev;

      const row = { ...newData[yearIdx][rowIdx] };
      const months = [...(row.months || [])];
      months[monthIdx] = value;

      const monthNumbers = months.map(val => parseFloat(val) || 0);
      const year = outsourceYears?.[yearIdx] || 0;

      const { op, ea1, ea2, yearEnd, total } = calculateYearSummary(monthNumbers, "Outsource", year);
      const conversionRates: { [year: number]: number } = {
        2024: 19.2,
        2025: 19.2,
        2026: 19.2,
      };
      const conversionRate = conversionRates[year] || 1;
      const fte = conversionRate > 0 ? total / conversionRate : 0;

      newData[yearIdx][rowIdx] = {
        ...row,
        months,
        totals: {
          op: op ? formatMoney(op) : "",
          ea1: ea1 ? formatMoney(ea1) : "",
          ea2: ea2 ? formatMoney(ea2) : "",
          yearEnd: yearEnd ? formatMoney(yearEnd) : "",
        },
        yearSummary: {
          plan: total,
          fte,
        },
      };

      return newData;
    });
  };
 React.useEffect(() => {
    console.log("✅ outsourceRowData updated:", outsourceRowData);
  }, [outsourceRowData]);
  return {
    outsourceYears,
    selectedOutsourceYears,
    outsourceRowData,
    addOutsourceTable,
    deleteSelectedOutsourceTables,
    toggleOutsourceYear,
    handleOutsourceMonthChange,
  };
};

export default useOutsourceLabour;
