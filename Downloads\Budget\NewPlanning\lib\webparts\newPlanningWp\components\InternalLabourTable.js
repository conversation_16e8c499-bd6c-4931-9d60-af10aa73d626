var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
import { sp } from "@pnp/sp";
import React from "react";
// === CONSTANTS ===
var editableLabourCategories = new Set([
    "Labor Plan (hrs)",
    "Labor Actual (hrs)",
    "Labor Plan (T-JPY)",
    "Labor Actual (T-JPY)"
]);
var stickyLeft1 = {
    position: "sticky",
    whiteSpace: "noWrap",
    left: 0,
    background: "#ccc",
    zIndex: 3,
    borderRight: "1px solid #ccc",
    padding: "4px"
};
var stickyLeft2 = {
    position: "sticky",
    whiteSpace: "noWrap",
    left: 0,
    background: "#ccc",
    zIndex: 3,
    borderRight: "1px solid #ccc",
    padding: "4px"
};
var stickyThStyle = {
    position: "sticky",
    whiteSpace: "noWrap",
    top: 0,
    background: "#ccc",
    zIndex: 2,
    borderBottom: "2px solid #999",
    padding: "4px"
};
var formatMoney = function (value) {
    if (typeof value !== "number" || isNaN(value))
        return "";
    return value.toLocaleString("en-US", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
};
var parseMoney = function (s) {
    if (s === undefined || s === null)
        return 0;
    if (typeof s === "number")
        return isFinite(s) ? s : 0;
    var cleaned = s.toString().replace(/,/g, "").trim();
    var n = parseFloat(cleaned);
    return isFinite(n) ? n : 0;
};
var fetchLabourRate = function (department, year) { return __awaiter(void 0, void 0, void 0, function () {
    var items, rate, error_1;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                _a.trys.push([0, 2, , 3]);
                console.log("Fetching labour rate for department: \"".concat(department, "\", year: ").concat(year));
                return [4 /*yield*/, sp.web.lists
                        .getByTitle("Labour Rate")
                        .items
                        .select("Title", "Year", "Department/Title")
                        .expand("Department")
                        .filter("Department/Title eq '".concat(department, "' and Year eq ").concat(year))
                        .top(1)
                        .get()];
            case 1:
                items = _a.sent();
                if (items.length > 0) {
                    rate = parseFloat(items[0].Title);
                    return [2 /*return*/, isNaN(rate) ? null : rate];
                }
                else {
                    console.warn("No labour rate found for department \"".concat(department, "\" and year \"").concat(year, "\"."));
                    return [2 /*return*/, null];
                }
                return [3 /*break*/, 3];
            case 2:
                error_1 = _a.sent();
                console.error("Error fetching labour rate:", error_1);
                return [2 /*return*/, null];
            case 3: return [2 /*return*/];
        }
    });
}); };
var updateTotalPlanHours = function (data) {
    return data.map(function (yearData) {
        var planRow = yearData.rows.find(function (r) { return r.category === "Labor Plan (hrs)"; });
        var total = planRow
            ? planRow.months.reduce(function (sum, val) { return sum + parseMoney(val); }, 0)
            : 0;
        return __assign(__assign({}, yearData), { totalPlanHours: total });
    });
};
// === COMPONENT ===
var InternalLabourTable = function (_a) {
    var labourData = _a.labourData, onDataChange = _a.onDataChange, selectedYears = _a.selectedYears, toggleYearSelection = _a.toggleYearSelection, addYear = _a.addYear, deleteYears = _a.deleteYears, calculateTotalsByQuarter = _a.calculateTotalsByQuarter, department = _a.department, isModal = _a.isModal, onUpdate = _a.onUpdate, onCancel = _a.onCancel;
    var fetchedYearsRef = React.useRef(new Set());
    React.useEffect(function () {
        var fetchRatesForAllYears = function () { return __awaiter(void 0, void 0, void 0, function () {
            var updatedData, updated, _loop_1, i, updatedWithTotal;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        updatedData = __spreadArray([], labourData, true);
                        updated = false;
                        _loop_1 = function (i) {
                            var year, rate;
                            return __generator(this, function (_b) {
                                switch (_b.label) {
                                    case 0:
                                        year = updatedData[i].year;
                                        if (fetchedYearsRef.current.has(year))
                                            return [2 /*return*/, "continue"];
                                        return [4 /*yield*/, fetchLabourRate(department, year)];
                                    case 1:
                                        rate = _b.sent();
                                        if (rate !== null) {
                                            updatedData[i].rows[0].labourRate = rate;
                                            updatedData[i].rows[1].labourRate = rate;
                                            [2, 3].forEach(function (budgetRowIdx) {
                                                var sourceRowIdx = budgetRowIdx - 2;
                                                var sourceRow = updatedData[i].rows[sourceRowIdx];
                                                var budgetRow = updatedData[i].rows[budgetRowIdx];
                                                var budgetMonths = sourceRow.months.map(function (m) {
                                                    return formatMoney(parseMoney(m) * rate);
                                                });
                                                var budgetTotals = calculateTotalsByQuarter(sourceRow.months, rate);
                                                updatedData[i].rows[budgetRowIdx] = __assign(__assign({}, budgetRow), { months: budgetMonths, totals: budgetTotals, labourRate: rate });
                                            });
                                            updated = true;
                                            fetchedYearsRef.current.add(year);
                                        }
                                        return [2 /*return*/];
                                }
                            });
                        };
                        i = 0;
                        _a.label = 1;
                    case 1:
                        if (!(i < updatedData.length)) return [3 /*break*/, 4];
                        return [5 /*yield**/, _loop_1(i)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        i++;
                        return [3 /*break*/, 1];
                    case 4:
                        if (updated) {
                            updatedWithTotal = updateTotalPlanHours(updatedData);
                            onDataChange(updatedWithTotal);
                        }
                        return [2 /*return*/];
                }
            });
        }); };
        var newYears = labourData
            .map(function (d) { return d.year; })
            .filter(function (y) { return !fetchedYearsRef.current.has(y); });
        if (department && newYears.length > 0) {
            fetchRatesForAllYears();
        }
    }, [department, labourData.map(function (d) { return d.year; }).join(",")]); // 👈 this makes it stable
    var handleMonthChange = function (yearIdx, rowIdx, monthIdx, value) {
        var updated = labourData.map(function (yearData, y) {
            var _a;
            if (y !== yearIdx)
                return yearData;
            // Prepare new months for the edited row (immutable)
            var editedRow = yearData.rows[rowIdx];
            var newMonths = __spreadArray([], editedRow.months, true);
            newMonths[monthIdx] = value;
            var rate = (_a = yearData.rows[0].labourRate) !== null && _a !== void 0 ? _a : 1;
            var newRows = yearData.rows.map(function (row, r) {
                if (r === rowIdx) {
                    // Update the edited row with new months and totals
                    var newTotals = calculateTotalsByQuarter(newMonths, r <= 1 ? 1 : rate);
                    return __assign(__assign({}, row), { months: newMonths, totals: newTotals });
                }
                if ((rowIdx === 0 || rowIdx === 1) && r === rowIdx + 2) {
                    // Use updated newMonths from edited row for budget rows
                    var budgetMonths = newMonths.map(function (m) { return formatMoney(parseMoney(m) * rate); });
                    var budgetTotals = calculateTotalsByQuarter(newMonths, rate);
                    return __assign(__assign({}, row), { months: budgetMonths, totals: budgetTotals, labourRate: rate });
                }
                return row;
            });
            return __assign(__assign({}, yearData), { rows: newRows });
        });
        var updatedWithTotal = updateTotalPlanHours(updated);
        onDataChange(updatedWithTotal);
    };
    var handleLabourRateChange = function (yearIdx, newRateStr) {
        var newRate = parseMoney(newRateStr);
        if (newRate <= 0) {
            alert("Please enter a valid labour rate greater than 0");
            return;
        }
        var updated = __spreadArray([], labourData, true);
        var yearData = updated[yearIdx];
        // Update labourRate on Plan and Actual rows
        yearData.rows[0].labourRate = newRate;
        yearData.rows[1].labourRate = newRate;
        // Recalculate budget rows (2 and 3) based on updated rate and current months
        [2, 3].forEach(function (budgetRowIdx) {
            var sourceRowIdx = budgetRowIdx - 2;
            var sourceRow = yearData.rows[sourceRowIdx];
            var budgetRow = yearData.rows[budgetRowIdx];
            var budgetMonths = sourceRow.months.map(function (m) {
                return formatMoney(parseMoney(m) * newRate);
            });
            var budgetTotals = calculateTotalsByQuarter(sourceRow.months, newRate);
            yearData.rows[budgetRowIdx] = __assign(__assign({}, budgetRow), { months: budgetMonths, totals: budgetTotals, labourRate: newRate });
        });
        var updatedWithTotal = updateTotalPlanHours(updated);
        onDataChange(updatedWithTotal);
    };
    return (React.createElement("div", null,
        labourData.map(function (data, yearIdx) {
            var _a, _b;
            return (React.createElement("div", { key: data.year, style: {
                    overflowX: "auto",
                    margin: "10px 0",
                    border: selectedYears.includes(data.year)
                        ? "2px solid blue"
                        : "1px solid #ccc",
                    padding: 10,
                    borderRadius: 4,
                } },
                React.createElement("tr", null, yearIdx !== 0 && (React.createElement(React.Fragment, null,
                    React.createElement("td", { style: stickyLeft2, colSpan: 16 },
                        React.createElement("label", { style: {
                                cursor: "pointer",
                                userSelect: "none",
                                display: "flex",
                                alignItems: "center",
                                gap: 8,
                            } },
                            React.createElement("input", { type: "checkbox", checked: selectedYears.includes(data.year), onChange: function () { return toggleYearSelection(data.year); } }),
                            React.createElement("strong", null, "Select table to delete")))))),
                React.createElement("table", { style: {
                        width: "100%",
                        borderCollapse: "collapse",
                        textAlign: "center",
                        minWidth: 900, // To allow horizontal scrolling for many columns
                    } },
                    React.createElement("thead", null,
                        React.createElement("tr", null,
                            React.createElement("th", { style: stickyLeft1 }, "Year"),
                            React.createElement("th", { style: stickyLeft2 }, "Total Year Plan (hrs)"),
                            React.createElement("th", { style: stickyThStyle }, "Total Year Plan (T-JPY)"),
                            React.createElement("th", { style: stickyThStyle }, "Labor Rate (T-JPY)")),
                        React.createElement("tr", null,
                            React.createElement("td", { style: stickyLeft1 }, data.year),
                            React.createElement("td", { style: stickyLeft2 }, formatMoney(data.totalPlanHours)),
                            React.createElement("td", null,
                                " ",
                                formatMoney(parseMoney((_a = data.rows.find(function (row) { return row.category === "Labor Plan (T-JPY)"; })) === null || _a === void 0 ? void 0 : _a.totals.total))),
                            React.createElement("td", null,
                                React.createElement("input", { type: "text", value: (_b = data.rows[0].labourRate) !== null && _b !== void 0 ? _b : "", onChange: function (e) { return handleLabourRateChange(yearIdx, e.target.value); }, style: { width: "80px", textAlign: "right" } }))),
                        React.createElement("tr", null,
                            React.createElement("th", { style: stickyLeft1 }, "Year"),
                            React.createElement("th", { style: stickyLeft2 }, "Labour Plan vs Actual"),
                            [
                                "JAN",
                                "FEB",
                                "MAR",
                                "APR",
                                "MAY",
                                "JUN",
                                "JUL",
                                "AUG",
                                "SEP",
                                "OCT",
                                "NOV",
                                "DEC",
                            ].map(function (m) { return (React.createElement("th", { key: m, style: stickyThStyle }, m)); }),
                            React.createElement("th", { style: stickyThStyle }, "OP"),
                            React.createElement("th", { style: stickyThStyle }, "EA1"),
                            React.createElement("th", { style: stickyThStyle }, "EA2"),
                            React.createElement("th", { style: stickyThStyle }, "Year-End"))),
                    React.createElement("tbody", null, data.rows.map(function (row, rowIdx) { return (React.createElement("tr", { key: rowIdx, style: { borderBottom: "1px solid #ddd" } },
                        React.createElement("td", { style: stickyLeft1 }, data.year),
                        React.createElement("td", { style: stickyLeft2 }, row.category),
                        row.months.map(function (val, mIdx) { return (React.createElement("td", { key: mIdx }, editableLabourCategories.has(row.category) ? (React.createElement("input", { value: val, onChange: function (e) {
                                return handleMonthChange(yearIdx, rowIdx, mIdx, e.target.value);
                            }, style: { width: "80px", textAlign: "right" } })) : (formatMoney(parseMoney(val))))); }),
                        React.createElement("td", null, row.totals.op),
                        React.createElement("td", null, row.totals.ea1),
                        React.createElement("td", null, row.totals.ea2),
                        React.createElement("td", null, row.totals.yearEnd))); })))));
        }),
        isModal ? (React.createElement("div", { style: { display: 'flex', justifyContent: 'flex-end', marginTop: '10px' } },
            React.createElement("button", { onClick: onUpdate, style: { marginRight: 8 } }, "Update"),
            React.createElement("button", { onClick: onCancel }, "Cancel"))) : (React.createElement("div", { style: { display: 'flex', justifyContent: 'flex-end', marginTop: '10px' } },
            React.createElement("button", { onClick: addYear }, "Add"),
            React.createElement("button", { onClick: deleteYears, disabled: selectedYears.length === 0 }, "Delete")))));
};
export default React.memo(InternalLabourTable);
//# sourceMappingURL=InternalLabourTable.js.map