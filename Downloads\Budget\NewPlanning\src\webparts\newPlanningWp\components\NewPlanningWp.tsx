import * as React from "react";
import styles from "./NewPlanningWp.module.scss";
import type { INewPlanningWpProps } from "./INewPlanningWpProps";
import { sp } from "@pnp/sp/presets/all";
import "@pnp/sp/webs";
import "@pnp/sp/lists";
import "@pnp/sp/items";
import AllPlanningWp from "./AllPlanning";
import { Icon } from "@fluentui/react";
import MaterialTable from "./Material";
import DepartmentTable from "./DepartmentTable";
import YearlyDetailModal from "./YearlyDetailModel";
import MaterialDetailModal from "./MaterialDetailModal";
import DepartmentDetailModal from "./DepartmentDetailModal";
import OutsourceDetailModal from "./OutsourceDetailModal";
//import LabourTable from './LabourTable';
import OutsourceLabourTable from "./RenderOutsourceTable";
import useOutsourceLabour from "./useOutsourceLabour";
//import isEqual from 'lodash/isEqual';
import { RowData } from "./RenderOutsourceTable";
//import{LabourRowData} from "./LabourTable"
import InternalLabourTable from "./InternalLabourTable";

sp.setup({
  sp: {
    baseUrl: "https://corptb.sharepoint.com/sites/apac-10747/",
  },
});

interface IActivityCategory {
  Id: number;
  Title: string;
}

interface IActivityType {
  id: number;
  label: string;
}

interface IFeatureDescription {
  id: number;
  label: string;
}

interface Totals {
  op: string;
  ea1: string;
  ea2: string;
  yearEnd: string;
  total: string; // optional if used somewhere
}

interface Row {
  spendingLevel: string;
  months: string[];
  totals: Totals;
  // You can add more fields if needed (e.g., yearSummary, labourRate)
}

interface YearData {
  year: number;
  rows: Row[];
}

export interface LabourYearData {
  year: number;
  rows: LabourRow[];
}

export interface LabourRow {
  category: string;
  months: string[];
  totals: Totals;
  labourRate?: number;
  year: number;
}

export const internalLabourCategories = [
  "Labor Plan (hrs)",
  "Labor Actual (hrs)",
  "Labor Plan (T-JPY)",
  "Labor Actual (T-JPY)",
];
const NewPlanningWp: React.FC<INewPlanningWpProps> = ({
  userDisplayName,
  initialPlanningNo,
}) => {
  const curYear = new Date().getFullYear(); // or any other assignment you want
  const {
    outsourceYears,
    selectedOutsourceYears,
    outsourceRowData,
    addOutsourceTable,
    deleteSelectedOutsourceTables,
    toggleOutsourceYear,
    handleOutsourceMonthChange,
  } = useOutsourceLabour(curYear);

  const Modal = ({
    children,
    onClose,
  }: {
    children: React.ReactNode;
    onClose: () => void;
  }) => (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        backgroundColor: "rgba(0,0,0,0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 9999,
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          padding: "20px",
          borderRadius: "8px",
          maxHeight: "90%",
          overflowY: "auto",
          width: "90%",
        }}
      >
        <div style={{ textAlign: "right" }}>
          <button
            onClick={onClose}
            style={{
              padding: "6px 12px",
              backgroundColor: "#a00",
              color: "#fff",
            }}
          >
            Close
          </button>
        </div>
        {children}
      </div>
    </div>
  );

  const [l3Options, setL3Options] = React.useState<any[]>([]);
  const [l4Options, setL4Options] = React.useState<any[]>([]);
  const [l3SelectedId, setL3SelectedId] = React.useState<number | "">("");
  const [l4SelectedId, setL4SelectedId] = React.useState<number | "">("");
  const [activeButton, setActiveButton] = React.useState<
    "project" | "department"
  >("project");
  const [showPopup, setShowPopup] = React.useState(false);
  const [showDepartmentPopup, setShowDepartmentPopup] = React.useState(false);
  const [errorPopupVisible, setErrorPopupVisible] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState("");
  const [textareaFocus, setTextareaFocus] = React.useState(false);

  const [costActivityData, setCostActivityData] = React.useState<any[]>([]);
  const [costCategoryOptions, setCostCategoryOptions] = React.useState<
    string[]
  >([]);
  const [selectedCostCategory, setSelectedCostCategory] =
    React.useState<string>("");
  const [selectedCostTypeId, setSelectedCostTypeId] = React.useState<
    number | ""
  >("");
  const [selectedCostDescriptionId, setSelectedCostDescriptionId] =
    React.useState<number | "">("");
  const [costTypeOptions, setCostTypeOptions] = React.useState<any[]>([]);
  const [costDescriptionOptions, setCostDescriptionOptions] = React.useState<
    any[]
  >([]);
  const [projectData, setProjectData] = React.useState<any[]>([]);
  const [projectOptions, setProjectOptions] = React.useState<
    { Id: number; Title: string }[]
  >([]);
  const [subProjectOptions, setSubProjectOptions] = React.useState<
    { Id: number; Title: string }[]
  >([]);
  const [selectedProjectId, setSelectedProjectId] = React.useState<number | "">(
    ""
  );
  const [selectedSubProjectId, setSelectedSubProjectId] = React.useState<
    number | ""
  >("");
  const [projectLeader, setProjectLeader] = React.useState<string>("");
  const [activityDescription, setActivityDescription] = React.useState("");
  const [Department, setDepartment] = React.useState("");

  const addButtonStyle: React.CSSProperties = {
    backgroundColor: "green",
    color: "white",
    padding: "6px 12px",
    border: "none",
    borderRadius: "3px",
    marginRight: "8px",
    cursor: "pointer",
  };

  const deleteButtonStyle: React.CSSProperties = {
    backgroundColor: "red",
    color: "white",
    padding: "6px 12px",
    border: "none",
    borderRadius: "3px",
    cursor: "pointer",
  };
  const modalBackdropStyle: React.CSSProperties = {
    position: "fixed",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.3)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  };

  const modalStyle: React.CSSProperties = {
    width: "80%", // Or a fixed width like '1000px' if needed
    maxHeight: "90vh",
    overflowX: "auto", // Enable horizontal scroll
    overflowY: "auto", // Also allow vertical scroll
    backgroundColor: "#fff",
    padding: "20px",
    borderRadius: "8px",
    boxShadow: "0 2px 10px rgba(0, 0, 0, 0.3)",
  };
  const errorModalStyle: React.CSSProperties = {
    backgroundColor: "white",
    padding: "30px",
    borderRadius: "8px",
    maxWidth: "400px",
    width: "100%",
    textAlign: "center",
    boxShadow: "0 2px 10px rgba(0,0,0,0.3)",
  };
  const formGridStyle: React.CSSProperties = {
    display: "grid",
    gridTemplateColumns: "repeat(3, 1fr)",
    gap: "16px",
    marginTop: "16px",
  };

  const okButtonStyle: React.CSSProperties = {
    backgroundColor: "#0078d4",
    color: "white",
    padding: "12px",
    marginRight: "8px",
    border: "none",
    borderRadius: "3px",
    cursor: "pointer",
    width: "10%",
  };

  const cancelButtonStyle: React.CSSProperties = {
    backgroundColor: "#d9534f",
    color: "white",
    padding: "6px",
    border: "none",
    borderRadius: "3px",
    cursor: "pointer",
    width: "10%",
  };

  const textareaStyle: React.CSSProperties = {
    width: "85%",
    padding: "8px",
    resize: "vertical",
    fontSize: "14px",
    borderRadius: "4px",
    border: "1px solid #ccc",
    outline: "none",
    borderColor: textareaFocus ? "#0078d4" : "#ccc", // Blue on focus
  };

  //Get Department
  React.useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const currentUser = await sp.web.currentUser.get();
        const currentUserId = currentUser.Id;

        const items = await sp.web.lists
          .getByTitle("User Registration")
          .items.select(
            "L3_x0020_Department_x0020_Code/Id",
            "L4_x0020_Cost_x0020_Block/Id"
          )
          .expand("L3_x0020_Department_x0020_Code", "L4_x0020_Cost_x0020_Block")
          .filter(`Requestor/Id eq ${currentUserId}`)
          .top(1)
          .get();

        const l3Id = items[0].L3_x0020_Department_x0020_Code?.Id;

        const l4Id = items[0].L4_x0020_Cost_x0020_Block?.Id;

        const [l3Data, l4Data] = await Promise.all([
          sp.web.lists
            .getByTitle("L3 Departments")
            .items.select("Id", "Title")
            .top(100)
            .get(),
          sp.web.lists
            .getByTitle("L4 Department")
            .items.select("Id", "Title")
            .top(100)
            .get(),
        ]);

        const filteredL3Options = l3Data.filter((dep) => dep.Id === l3Id);
        const filteredL4Options = l4Data.filter((dep) => dep.Id === l4Id);
        if (filteredL3Options.length > 0) {
          setDepartment(filteredL3Options[0].Title); // Set department name here
        } else {
          setDepartment("");
        }
        setL3Options(filteredL3Options);
        setL4Options(filteredL4Options);
      } catch (error) {
        console.error("Error fetching department data:", error);
      }
    };

    fetchDepartments();
  }, [userDisplayName]);

  //CostActivity
  React.useEffect(() => {
    const fetchCostActivityData = async () => {
      try {
        const response = await sp.web.lists
          .getByTitle("Cost_Activity_Information")
          .items.select(
            "Cost_x0020_Category",
            "Activity_x0020_Category/Title",
            "Activity_x0020_Type/Title",
            "Cost_x0020_Type/Title",
            "Cost_x0020_Description/Title",
            "Cost_x0020_Type/Id",
            "Cost_x0020_Description/Id",
            "Feature_x0020_Description/Title"
          )
          .expand(
            "Feature_x0020_Description",
            "Activity_x0020_Category",
            "Activity_x0020_Type",
            "Cost_x0020_Description",
            "Cost_x0020_Type"
          )
          .orderBy("Cost_x0020_Type/Title", true)
          .top(4999)
          .get();
        const uniqueCategories = Array.from(
          new Set(response.map((item) => item.Cost_x0020_Category))
        ).filter(Boolean); // Remove null/undefined
        //setCostActivityData(response);
        setCostActivityData(response);
        setCostCategoryOptions(uniqueCategories);
      } catch (error) {
        console.error("Error fetching Cost Activity data:", error);
      }
    };

    fetchCostActivityData();
  }, []);

  //PRoject NAme
  React.useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await sp.web.lists
          .getByTitle("ProjectList")
          .items.select(
            "ProjectName/Title",
            "ProjectName/Id",
            "SubProjectName/Title",
            "SubProjectName/Id"
          )
          .expand("ProjectName", "SubProjectName")
          .top(4999)
          .get();

        setProjectData(response);

        const uniqueProjects = Array.from(
          new Map(
            response.map((item) => [item.ProjectName?.Id, item.ProjectName])
          ).values()
        ) as { Id: number; Title: string }[];
        uniqueProjects.sort((a, b) => a.Title.localeCompare(b.Title));
        setProjectOptions(uniqueProjects);
      } catch (error) {
        console.error("Error fetching ProjectList data:", error);
      }
    };

    fetchProjects();
  }, []);
  //Cost Category
  React.useEffect(() => {
    if (selectedCostCategory) {
      const filtered = costActivityData.filter(
        (item) => item.Cost_x0020_Category === selectedCostCategory
      );

      const uniqueTypes = Array.from(
        new Map(
          filtered.map((item) => [
            item.Cost_x0020_Type?.Id,
            item.Cost_x0020_Type,
          ])
        ).values()
      );

      setCostTypeOptions(uniqueTypes);
      setSelectedCostTypeId(""); // Reset selection
      setCostDescriptionOptions([]);
      setSelectedCostDescriptionId("");
    } else {
      setCostTypeOptions([]);
      setSelectedCostTypeId("");
      setCostDescriptionOptions([]);
      setSelectedCostDescriptionId("");
    }
  }, [selectedCostCategory]);

  React.useEffect(() => {
    if (selectedCostTypeId) {
      const filtered = costActivityData.filter(
        (item) => item.Cost_x0020_Type?.Id === selectedCostTypeId
      );

      const uniqueDescriptions = Array.from(
        new Map(
          filtered.map((item: { Cost_x0020_Description: { Id: any } }) => [
            item.Cost_x0020_Description?.Id,
            item.Cost_x0020_Description,
          ])
        ).values()
      );

      setCostDescriptionOptions(uniqueDescriptions);
      setSelectedCostDescriptionId("");
    } else {
      setCostDescriptionOptions([]);
      setSelectedCostDescriptionId("");
    }
  }, [selectedCostTypeId]);

  React.useEffect(() => {
    if (selectedProjectId) {
      const filtered = projectData.filter(
        (item) => item.ProjectName?.Id === selectedProjectId
      );

      const uniqueSubs = Array.from(
        new Map(
          filtered.map((item) => [item.SubProjectName?.Id, item.SubProjectName])
        ).values()
      ) as { Id: number; Title: string }[];
      uniqueSubs.sort((a, b) => a.Title.localeCompare(b.Title));

      setSubProjectOptions(uniqueSubs);
      setSelectedSubProjectId("");

      // Set Project Leader from the first matching item
      if (filtered.length > 0 && filtered[0].ProjectLeader?.Title) {
        setProjectLeader(filtered[0].ProjectLeader.Title);
      } else {
        setProjectLeader("");
      }
    } else {
      setSubProjectOptions([]);
      setSelectedSubProjectId("");
      setProjectLeader("");
    }
  }, [selectedProjectId, projectData]);

  const [activityCategories, setActivityCategories] = React.useState<
    IActivityCategory[]
  >([]);
  const [selectedActivityCategoryId, setSelectedActivityCategoryId] =
    React.useState<number | "">("");
  React.useEffect(() => {
    const fetchActivityCategories = async () => {
      try {
        const items = await sp.web.lists
          .getByTitle("Activity Category")
          .items.select("Id", "Title")
          .top(4999)
          .orderBy("Title", true)
          .get();

        // Add default option
        const options: IActivityCategory[] = [
          { Id: 0, Title: "Select Category" },
          ...items,
        ];
        setActivityCategories(options);
      } catch (error) {
        console.error("Error fetching activity categories:", error);
      }
    };

    fetchActivityCategories();
  }, []);

  const [activityTypes, setActivityTypes] = React.useState<IActivityType[]>([]);
  const [selectedActivityTypeId, setSelectedActivityTypeId] =
    React.useState<number>(0);

  React.useEffect(() => {
    const fetchActivityTypes = async () => {
      try {
        const items = await sp.web.lists
          .getByTitle("Activity Type")
          .items.select("Id", "Title")
          .top(4999)
          .orderBy("Title", true)
          .get();

        if (items.length > 0) {
          const mapped: IActivityType[] = [
            { id: 0, label: "Select Type" },
            ...items.map((item) => ({
              id: item.Id,
              label: item.Title,
            })),
          ];

          setActivityTypes(mapped);
          setSelectedActivityTypeId(0); // set default value
        } else {
          alert("No Activity Types found.");
        }
      } catch (error) {
        console.error("Error fetching Activity Types: ", error);
      }
    };

    fetchActivityTypes();
  }, []);

  const [featureDescriptions, setFeatureDescriptions] = React.useState<
    IFeatureDescription[]
  >([]);
  const [selectedFeatureDescriptionId, setSelectedFeatureDescriptionId] =
    React.useState<number>(0);

  React.useEffect(() => {
    const fetchFeatureDescriptions = async () => {
      try {
        const items = await sp.web.lists
          .getByTitle("Feature Description")
          .items.select("Id", "Title")
          .top(4999)
          .orderBy("Title", true)
          .get();

        if (items.length > 0) {
          const mapped: IFeatureDescription[] = [
            { id: 0, label: "Select Feature Description" },
            ...items.map((item) => ({
              id: item.Id,
              label: item.Title,
            })),
          ];

          setFeatureDescriptions(mapped);
          setSelectedFeatureDescriptionId(0); // Default selected
        } else {
          alert("No Feature Descriptions found.");
        }
      } catch (error) {
        console.error("Error fetching Feature Descriptions: ", error);
      }
    };

    fetchFeatureDescriptions();
  }, []);
  const [, setMaterialIndex] = React.useState(0);
  const [, setDepartmentIndex] = React.useState(0);
  const [, setOutsourceIndex] = React.useState(0);
  const [, setLabourIndex] = React.useState(0);
  const [planningNo, setPlanningNo] = React.useState<string>(
    initialPlanningNo || ""
  );
  React.useEffect(() => {
    if (initialPlanningNo) {
      setPlanningNo(initialPlanningNo);
    }
  }, [initialPlanningNo]);

  React.useEffect(() => {
    if (!selectedCostCategory || planningNo) return;

    let prefix = "";
    let indexUpdater: React.Dispatch<React.SetStateAction<number>> | null =
      null;

    switch (selectedCostCategory) {
      case "Material":
        prefix = "RDM_";
        indexUpdater = setMaterialIndex;
        break;
      case "Department":
        prefix = "RDD_";
        indexUpdater = setDepartmentIndex;
        break;
      case "Outsource":
        prefix = "RDO_";
        indexUpdater = setOutsourceIndex;
        break;
      case "Labour":
        prefix = "RDL_";
        indexUpdater = setLabourIndex;
        break;
    }

    if (indexUpdater) {
      indexUpdater((prev) => {
        const newIndex = prev + 1;
        setPlanningNo(prefix + newIndex);
        return newIndex;
      });
    }
  }, [selectedCostCategory, planningNo]);

  const renderPopupForm = () => {
    if (!showPopup) return null;

    return (
      <div style={modalBackdropStyle}>
        <div style={modalStyle}>
          <h2
            style={{
              textAlign: "center",
              backgroundColor: "#0078d4",
              color: "#fff",
              padding: "10px",
            }}
          >
            PROJECT BUDGET
          </h2>
          <div style={formGridStyle}>
            <div className={styles.formGroup}>
              <label>Cost Category *</label>
              <select
                value={selectedCostCategory}
                onChange={(e) => {
                  setSelectedCostCategory(e.target.value);
                  setPlanningNo(""); // Reset planning number when category changes
                }}
              >
                <option value="">Select Category</option>
                {costCategoryOptions
                  .filter((cat) => !cat.toLowerCase().includes("department"))
                  .map((cat, idx) => (
                    <option key={idx} value={cat}>
                      {cat}
                    </option>
                  ))}
              </select>
            </div>
            <div className={styles.formGroup}>
              <label>Cost Type *</label>
              <select
                value={selectedCostTypeId}
                onChange={(e) => setSelectedCostTypeId(Number(e.target.value))}
              >
                <option value="">Select Type</option>
                {costTypeOptions.map((type) => (
                  <option key={type.Id} value={type.Id}>
                    {type.Title}
                  </option>
                ))}
              </select>
            </div>
            <div className={styles.formGroup}>
              <label>Cost Description *</label>
              <select
                value={selectedCostDescriptionId}
                onChange={(e) =>
                  setSelectedCostDescriptionId(Number(e.target.value))
                }
              >
                <option value="">Select Cost Description</option>
                {costDescriptionOptions.map((desc) => (
                  <option key={desc.Id} value={desc.Id}>
                    {desc.Title}
                  </option>
                ))}
              </select>
            </div>
            <div className={styles.formGroup}>
              <label>Project Name *</label>
              <select
                value={selectedProjectId}
                onChange={(e) => {
                  const val = e.target.value;
                  setSelectedProjectId(val === "" ? "" : Number(val));
                }}
              >
                <option value="">Select Project</option>
                {projectOptions.map((project) => (
                  <option key={project.Id} value={project.Id}>
                    {project.Title}
                  </option>
                ))}
              </select>
            </div>
            <div className={styles.formGroup}>
              <label>Sub Project Name *</label>
              <select
                value={selectedSubProjectId}
                onChange={(e) =>
                  setSelectedSubProjectId(Number(e.target.value))
                }
              >
                <option value="">Select Sub Project</option>
                {subProjectOptions.map((sub) => (
                  <option key={sub.Id} value={sub.Id}>
                    {sub.Title}
                  </option>
                ))}
              </select>
              {projectLeader && (
                <>
                  <div className={styles.formGroup}>
                    <label>Project Leader</label>
                    <input type="text" value={projectLeader} readOnly />
                  </div>
                  <div className={styles.formGroup}>
                    <label>Planning No</label>
                    <input type="text" value={planningNo} readOnly />
                  </div>
                </>
              )}
            </div>
            <div></div>
            <div className={styles.formGroup}>
              <label>Activity Category *</label>
              <select
                value={selectedActivityCategoryId}
                onChange={(e) =>
                  setSelectedActivityCategoryId(Number(e.target.value))
                }
              >
                {activityCategories.map((cat) => (
                  <option key={cat.Id} value={cat.Id}>
                    {cat.Title}
                  </option>
                ))}
              </select>
            </div>
            <div className={styles.formGroup}>
              <label>Activity Type *</label>
              <select
                value={selectedActivityTypeId}
                onChange={(e) =>
                  setSelectedActivityTypeId(Number(e.target.value))
                }
              >
                {activityTypes.map((type) => (
                  <option key={type.id} value={type.id}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
            <div className={styles.formGroup}>
              <label>Feature Description *</label>
              <select
                style={{ position: "relative", zIndex: 10 }}
                value={selectedFeatureDescriptionId}
                onChange={(e) =>
                  setSelectedFeatureDescriptionId(Number(e.target.value))
                }
              >
                {featureDescriptions.map((desc) => (
                  <option key={desc.id} value={desc.id}>
                    {desc.label}
                  </option>
                ))}
              </select>
            </div>

            <div className={styles.formGroup}>
              <label>Activity Description *</label>
              <textarea
                rows={3}
                style={textareaStyle}
                value={activityDescription}
                onChange={(e) => setActivityDescription(e.target.value)}
                onFocus={() => setTextareaFocus(true)}
                onBlur={() => setTextareaFocus(false)}
              />
            </div>
            <div className={styles.formGroup}>
              <label>Free Space/Remark</label>
              <textarea
                rows={3}
                style={textareaStyle}
                onFocus={() => setTextareaFocus(true)}
                onBlur={() => setTextareaFocus(false)}
              />
            </div>
            <div className={styles.formGroup}>
              <label>SLA/C4D HDEP</label>
              <textarea
                rows={3}
                style={textareaStyle}
                onFocus={() => setTextareaFocus(true)}
                onBlur={() => setTextareaFocus(false)}
              />
            </div>
          </div>
          <div style={{ marginTop: "20px" }}>
            <div>
              {selectedCostCategory === "Material" && (
                <MaterialTable
                  index={0}
                  tblCounter={tableYears.length}
                  curYear={curYear}
                  createMaterialTable={createMaterialTable}
                  deleteSelectedTables={deleteSelectedTables}
                  selectedYears={selectedYears}
                  toggleYearSelection={toggleYearSelection}
                  tableYears={tableYears}
                  rowData={rowData}
                  spendingLevels={spendingLevels}
                  editableLevels={editableLevels}
                  handleMonthChange={handleMonthChange}
                  isModal={false}
                />
              )}
            </div>
          </div>
          <div style={{ marginTop: "20px" }}>
            <div>
              {selectedCostCategory === "Outsourcing Labor" && (
                <OutsourceLabourTable
                  outsourceYears={outsourceYears}
                  selectedOutsourceYears={selectedOutsourceYears}
                  outsourceRowData={outsourceRowData}
                  addOutsourceTable={addOutsourceTable}
                  deleteSelectedOutsourceTables={deleteSelectedOutsourceTables}
                  toggleOutsourceYear={toggleOutsourceYear}
                  handleOutsourceMonthChange={handleOutsourceMonthChange}
                />
              )}
            </div>
            <div>
              {selectedCostCategory === "Internal Labor" && (
                <InternalLabourTable
                  labourData={internalLabourData}
                  onDataChange={handleInternalLabourDataChange}
                  selectedYears={selectedInternalYears}
                  toggleYearSelection={toggleInternalYearSelection}
                  addYear={addInternalYear}
                  deleteYears={deleteSelectedInternalYears}
                  calculateTotalsByQuarter={calculateTotalsByQuarter}
                  department={Department}
                  isModal={false}
                />
              )}
            </div>
          </div>
          <div style={{ marginTop: "20px" }}>
            <div>
              {/*  <div style={{ marginTop: '20px' }}>
  {selectedCostCategory === "Internal Labor" && renderLabourTable()}
</div> */}
            </div>
          </div>
          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
              marginTop: "12px",
            }}
          >
            <button style={okButtonStyle} onClick={handleSave}>
              Ok
            </button>
            <button
              style={cancelButtonStyle}
              onClick={() => setShowPopup(false)}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  };
  const renderDepartmentPopupForm = () => {
    if (!showDepartmentPopup) return null;

    return (
      <div style={modalBackdropStyle}>
        <div style={modalStyle}>
          <h2
            style={{
              textAlign: "center",
              backgroundColor: "#0078d4",
              color: "#fff",
              padding: "10px",
            }}
          >
            DEPARTMENT BUDGET
          </h2>

          <div style={formGridStyle}>
            <div className={styles.formGroup}>
              <label>Cost Category *</label>
              {/* <select  value={selectedCostCategory}
                onChange={(e) => {
                  setSelectedCostCategory(e.target.value);setPlanningNo(""); 
                  }}
                >
                <option value="">Select Category</option>
                {costCategoryOptions.map((cat, idx) => (
                  <option key={idx} value={cat}>
                    {cat}
                  </option>
                ))}
        </select> */}
              <select
                value={selectedCostCategory}
                onChange={(e) => {
                  setSelectedCostCategory(e.target.value);
                  setPlanningNo("");
                }}
              >
                <option value="">Select Category</option>
                {costCategoryOptions
                  .filter((cat) => cat.toLowerCase().includes("department"))
                  .map((cat, idx) => (
                    <option key={idx} value={cat}>
                      {cat}
                    </option>
                  ))}
              </select>
            </div>
            <div className={styles.formGroup}>
              <label>Cost Type *</label>
              <select
                value={selectedCostTypeId}
                onChange={(e) => setSelectedCostTypeId(Number(e.target.value))}
              >
                <option value="">Select Type</option>
                {costTypeOptions.map((type) => (
                  <option key={type.Id} value={type.Id}>
                    {type.Title}
                  </option>
                ))}
              </select>
            </div>
            <div className={styles.formGroup}>
              <label>Cost Description *</label>
              <select
                value={selectedCostDescriptionId}
                onChange={(e) =>
                  setSelectedCostDescriptionId(Number(e.target.value))
                }
              >
                <option value="">Select Cost Description</option>
                {costDescriptionOptions.map((desc) => (
                  <option key={desc.Id} value={desc.Id}>
                    {desc.Title}
                  </option>
                ))}
              </select>
            </div>
            <div className={styles.formGroup}>
              <label>Activity Description *</label>
              <textarea
                rows={3}
                style={textareaStyle}
                value={activityDescription}
                onChange={(e) => setActivityDescription(e.target.value)}
                onFocus={() => setTextareaFocus(true)}
                onBlur={() => setTextareaFocus(false)}
              />
            </div>
            <div className={styles.formGroup}>
              <label>Free Space/Remark</label>
              <textarea
                rows={3}
                style={textareaStyle}
                onFocus={() => setTextareaFocus(true)}
                onBlur={() => setTextareaFocus(false)}
              />
            </div>
            <div className={styles.formGroup}>
              <label>SLA/C4D HDEP</label>
              <textarea
                rows={3}
                style={textareaStyle}
                onFocus={() => setTextareaFocus(true)}
                onBlur={() => setTextareaFocus(false)}
              />
            </div>
          </div>

          {/* Conditionally show Department Labour tables if needed */}
          <div style={{ marginTop: "20px" }}>
            {selectedCostCategory === "Department" && (
              <DepartmentTable
                index={0}
                tblCounter={tableYears.length}
                curYear={curYear}
                createDepartmentTable={createDepartmentTable}
                deleteSelectedTables={deleteSelectedTables}
                selectedYears={selectedYears}
                toggleYearSelection={toggleYearSelection}
                tableYears={tableYears}
                rowData={rowData}
                spendingLevels={spendingLevels}
                editableLevels={editableLevels}
                handleMonthChange={handleMonthChange}
              />
            )}
          </div>

          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
              marginTop: "12px",
            }}
          >
            <button style={okButtonStyle} onClick={handleSaveDepartmentBudget}>
              Ok
            </button>
            <button
              style={cancelButtonStyle}
              onClick={() => setShowDepartmentPopup(false)}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  };

  //ERROR Popup
  const renderErrorPopup = () => {
    if (!errorPopupVisible) return null;

    return (
      <div style={modalBackdropStyle}>
        <div style={errorModalStyle}>
          <div style={{ textAlign: "center" }}>
            <div style={{ fontSize: "50px", color: "#f44336" }}>❌</div>
            <p style={{ fontSize: "18px", fontWeight: 500 }}>{errorMessage}</p>
            <button
              style={okButtonStyle}
              onClick={() => setErrorPopupVisible(false)}
            >
              OK
            </button>
          </div>
        </div>
      </div>
    );
  };

  //MATERIAL TABLE FUNCTIOANLITY
  const [tableYears, setTableYears] = React.useState<number[]>([curYear]);
  const [selectedYears, setSelectedYears] = React.useState<number[]>([]);

  const createMaterialTable = () => {
    setTableYears((prev) => {
      const lastYear = prev[prev.length - 1];
      if (lastYear >= 2035) return prev; // Optional: limit to 2035
      return [...prev, lastYear + 1];
    });
  };

  const createDepartmentTable = () => {
    setTableYears((prev) => {
      const lastYear = prev[prev.length - 1];
      if (lastYear >= 2035) return prev; // Optional: limit to 2035
      return [...prev, lastYear + 1];
    });
  };
  const toggleYearSelection = (year: number) => {
    setSelectedYears((prev) =>
      prev.includes(year) ? prev.filter((y) => y !== year) : [...prev, year]
    );
  };
  const deleteSelectedTables = () => {
    setTableYears((prev) =>
      prev.filter((year) => !selectedYears.includes(year))
    );
    setSelectedYears([]); // Clear selection after deletion
  };
  const spendingLevels = [
    "SL2_Committee approval Plan",
    "SL2_Committee approval Actual",
    "SL3_PR release Plan",
    "SL3_PR release Actual - (cbFC)",
    "SL4 Payment Plan",
    "SL4 Payment Actual -(cbFC)",
  ];

  const editableLevels = new Set([
    "SL2_Committee approval Plan",
    "SL3_PR release Plan",
    "SL4 Payment Plan",
  ]);
  const calculateYearSummary = (
    monthlyValues: number[],
    spendingLevel: string,
    year: number,
    planValue?: string // optional like "SL4 Payment Plan"
  ) => {
    const now = new Date();
    const currentYear = now.getFullYear();
    const curMonth = now.getMonth(); // 0=Jan, 11=Dec

    const total = monthlyValues.reduce(
      (sum, val) => sum + (isNaN(val) ? 0 : val),
      0
    );

    let op = 0,
      ea1 = 0,
      ea2 = 0,
      yearEnd = 0;

    if (year === currentYear) {
      if (curMonth === 0) {
        op = total;
      } else if (curMonth > 0 && curMonth < 6) {
        ea1 = total;
      } else if (curMonth >= 6 && curMonth < 8) {
        ea2 = total;
      } else if (curMonth >= 8 && curMonth <= 11) {
        yearEnd = total;
      }
    } else {
      op = total;
    }

    return {
      op,
      ea1,
      ea2,
      yearEnd,
      total,
    };
  };

  const [rowData, setRowData] = React.useState(
    tableYears.map((year) => ({
      year,
      rows: spendingLevels.map((level) => ({
        spendingLevel: level,
        months: Array(12).fill(""),
        totals: { op: "", ea1: "", ea2: "", yearEnd: "", total: "" },
      })),
    }))
  );
  const formatMoney = (value: number | undefined | null): string => {
    if (typeof value !== "number" || isNaN(value)) return "";
    return value.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const handleMonthChange = (
    yearIdx: number,
    rowIdx: number,
    monthIdx: number,
    value: string
  ) => {
    setRowData((prev) => {
      const newData = [...prev];
      const row = newData[yearIdx].rows[rowIdx];

      const newMonths = [...row.months];
      newMonths[monthIdx] = value;

      const monthNumbers = newMonths.map((val) => parseFloat(val) || 0);

      const { op, ea1, ea2, yearEnd, total } = calculateYearSummary(
        monthNumbers,
        row.spendingLevel,
        newData[yearIdx].year,
        row.spendingLevel
      );

      newData[yearIdx].rows[rowIdx] = {
        ...row,
        months: newMonths,
        totals: {
          op: op ? formatMoney(op) : "",
          ea1: ea1 ? formatMoney(ea1) : "",
          ea2: ea2 ? formatMoney(ea2) : "",
          yearEnd: yearEnd ? formatMoney(yearEnd) : "",
          total:
            row.spendingLevel === "SL4 Payment Plan" && total
              ? formatMoney(total)
              : "",
        },
      };

      return newData;
    });
  };

  /** ---------- AFTER SAVE FUNCTIONALITY ---------- **/
  const [yearlyPlanningDate, setYearlyPlanningDate] = React.useState<any[]>([]);
  const [, setYearlyData] = React.useState<any[]>([]);

  const parseMoney = (s: string | number | undefined): number => {
    if (s === undefined || s === null) return 0;
    if (typeof s === "number") return isFinite(s) ? s : 0;
    const cleaned = s.replace(/,/g, "").trim();
    const n = parseFloat(cleaned);
    return isFinite(n) ? n : 0;
  };

  const isEmpty = (v: any) => v === undefined || v === null || v === "";

  const buildRowsForYear = (yearIdx: number) => {
    const y = rowData[yearIdx];
    const rowPlanningData: any[] = [];
    const rowbreakupData: any[] = [];

    // sum "Total Year Plan (T-JPY)" for this year from your “SL4 Payment Plan” totals
    const sl4 = y.rows.find((r: any) => r.spendingLevel === "SL4 Payment Plan");
    const initialJPY = parseMoney(sl4?.totals?.total || "");
    const totalHrs = 0; // TODO: if you have hours for Labour, set it here
    const labourRate = ""; // TODO: if you have a selected rate for Labour, set it here

    y.rows.forEach((r: any, idx: number) => {
      const months = (r.months || []).map((m: string) => parseMoney(m));
      const val = {
        initialJPY,
        totalHrs,
        LabourRate: labourRate,
        Year: y.year,
        Type: r.spendingLevel,
        jan: months[0] || 0,
        feb: months[1] || 0,
        mar: months[2] || 0,
        apr: months[3] || 0,
        may: months[4] || 0,
        jun: months[5] || 0,
        jul: months[6] || 0,
        aug: months[7] || 0,
        sep: months[8] || 0,
        oct: months[9] || 0,
        nov: months[10] || 0,
        dec: months[11] || 0,
        op: parseMoney(r.totals?.op) || 0,
        ea1: parseMoney(r.totals?.ea1) || 0,
        ea2: parseMoney(r.totals?.ea2) || 0,
        yearEnd: parseMoney(r.totals?.yearEnd) || 0,
        comments: "",
      };

      // “rowPlanningData” used to be {key, value}; keep a stable key like in your old code
      const key = `${planningNo}_${selectedCostCategory}_${y.year}_${idx + 1}`;
      rowPlanningData.push({ key, value: val });
      rowbreakupData.push(val);
    });

    return { rowPlanningData, rowbreakupData, initialJPY };
  };

  const buildAllMonthlyArrays = () => {
    const monthlyPlanningData: any[] = [];
    const monthlyBreakData: any[] = [];

    rowData.forEach((yBlock: any, yearIdx: number) => {
      const { rowPlanningData, rowbreakupData } = buildRowsForYear(yearIdx);

      const tableKey = `${planningNo}_${selectedCostCategory}_${yearIdx}`;
      monthlyPlanningData.push({ key: tableKey, value: rowPlanningData });

      const tblYear = yBlock.year; // Angular used the selected ddlYear as key
      monthlyBreakData.push({ key: tblYear, value: rowbreakupData });
    });

    return { monthlyPlanningData, monthlyBreakData };
  };

  const loadRowsFromMonthlyJSONData = (data: any[]): YearData[] => {
    return data.map((block) => ({
      year: Number(block.key),
      rows: Array.isArray(block?.value)
        ? block.value.map((entry: any) => ({
            spendingLevel: entry.Type,
            months:
              entry.monthly?.length === 12 ? entry.monthly : Array(12).fill(""),
            totals: {
              total: entry.initialJPY ?? "",
              op: entry.op ?? "",
              ea1: entry.ea1 ?? "",
              ea2: entry.ea2 ?? "",
              yearEnd: entry.yearEnd ?? "",
            },
          }))
        : [], // fallback if block.value is not an array
    }));
  };
  const [showYearlyDetailModal, setShowYearlyDetailModal] =
    React.useState(false);
  const [showMaterialDetailModal, setShowMaterialDetailModal] =
    React.useState(false);
  const [showDepartmentDetailModal, setShowDepartmentDetailModal] =
    React.useState(false);
  const [showOutsourceDetailModal, setShowOutsourceDetailModal] =
    React.useState(false);
  const validateMaterialData = (rowData: any[]): string | null => {
    if (isEmpty(selectedCostCategory)) return "Please select Cost Category.";
    if (isEmpty(selectedCostTypeId)) return "Please select Cost Type.";
    if (isEmpty(selectedCostDescriptionId))
      return "Please select Cost Description.";
    if (isEmpty(selectedProjectId)) return "Please select Project.";
    if (isEmpty(selectedSubProjectId)) return "Please select Sub Project.";
    if (isEmpty(selectedActivityCategoryId))
      return "Please select Activity Category.";
    if (isEmpty(selectedActivityTypeId)) return "Please select Activity Type.";
    if (isEmpty(selectedFeatureDescriptionId))
      return "Please select Feature Description.";
    if (isEmpty(activityDescription?.trim()))
      return "Please enter Activity Description.";

    if (!rowData?.length) return "Please add at least one year table.";

    for (const y of rowData) {
      const sl4 = y.rows.find(
        (r: any) => r.spendingLevel === "SL4 Payment Plan"
      );
      const total = parseMoney(sl4?.totals?.total || "");
      if (total <= 0) {
        return `Enter monthly values so that 'SL4 Payment Plan' has a total for year ${y.year}.`;
      }
    }

    return null;
  };
  // SAVE HANDLERS
  const handleSaveMaterialBudget = () => {
    const err = validateMaterialData(rowData);
    if (err) {
      setErrorMessage(err);
      setErrorPopupVisible(true);
      return;
    }

    const { monthlyPlanningData, monthlyBreakData } = buildAllMonthlyArrays();

    const payload = {
      Key: planningNo,
      value: {
        RequestId: 0,
        PlanningNumber: planningNo,
        ActivityCategory: selectedActivityCategoryId || 0,
        ActivityType: selectedActivityTypeId || 0,
        FeatureDescription: selectedFeatureDescriptionId || 0,
        ActivityContent: activityDescription,
        Remarks: "", // TODO: wire to state
        SLA_C4D_x0020_HDEP: "", // TODO: wire to state
        CostCategory: selectedCostCategory,
        CostType: selectedCostTypeId,
        CostDescription: selectedCostDescriptionId,
        projCategory: 0,
        projCluster: 0,
        projName: selectedProjectId || 0,
        subprojName: selectedSubProjectId || 0,
        projectLeaderID: projectLeader || "",
        planApproval: "0", // Always "0" for project-based
        MonthlyBreakup: monthlyPlanningData,
        MonthlyJSONData: monthlyBreakData,
      },
    };

    setYearlyPlanningDate((prev) => {
      const next = [...prev, payload];
      setYearlyData(next);
      return next;
    });

    setShowPopup(false);
  };
  //Department after save
  //VALIDATION FOR SAVE
  const validateDepartmentData = (rowData: any[]): string | null => {
    if (isEmpty(selectedCostCategory)) return "Please select Cost Category.";
    if (isEmpty(selectedCostTypeId)) return "Please select Cost Type.";
    if (isEmpty(selectedCostDescriptionId))
      return "Please select Cost Description.";
    if (isEmpty(selectedActivityTypeId)) return "Please select Activity Type.";
    if (isEmpty(selectedFeatureDescriptionId))
      return "Please select Feature Description.";
    if (isEmpty(activityDescription?.trim()))
      return "Please enter Activity Description.";

    if (!rowData?.length) return "Please add at least one year table.";

    for (const y of rowData) {
      const sl4 = y.rows.find(
        (r: any) => r.spendingLevel === "SL4 Payment Plan"
      );
      const total = parseMoney(sl4?.totals?.total || "");
      if (total <= 0) {
        return `Enter monthly values so that 'SL4 Payment Plan' has a total for year ${y.year}.`;
      }
    }

    return null;
  };
  const handleSaveDepartmentBudget = () => {
    const err = validateDepartmentData(rowData);
    if (err) {
      setErrorMessage(err);
      setErrorPopupVisible(true);
      return;
    }

    const { monthlyPlanningData, monthlyBreakData } = buildAllMonthlyArrays();
    console.log(monthlyBreakData, monthlyPlanningData);
    const payload = {
      Key: planningNo,
      value: {
        RequestId: 0,
        PlanningNumber: planningNo,
        ActivityCategory: selectedActivityCategoryId || 0,
        ActivityType: selectedActivityTypeId || 0,
        FeatureDescription: selectedFeatureDescriptionId || 0,
        ActivityContent: activityDescription,
        Remarks: "", // TODO: wire to state
        SLA_C4D_x0020_HDEP: "", // TODO: wire to state
        CostCategory: selectedCostCategory,
        CostType: selectedCostTypeId,
        CostDescription: selectedCostDescriptionId,
        projCategory: 0,
        projCluster: 0,
        projName: selectedProjectId || 0,
        subprojName: selectedSubProjectId || 0,
        projectLeaderID: projectLeader || "",
        planApproval: "0", // Always "0" for project-based
        MonthlyBreakup: monthlyPlanningData,
        MonthlyJSONData: monthlyBreakData,
      },
    };

    setYearlyPlanningDate((prev) => {
      const next = [...prev, payload];
      setYearlyData(next);
      return next;
    });

    setShowDepartmentPopup(false);
  };
  const renderDepartmentYearlyPlanSummary = () => {
    return (
      <table
        style={{ borderCollapse: "collapse", width: "100%", marginTop: "20px" }}
      >
        <thead>
          <tr style={{ backgroundColor: "#f0f0f0", textAlign: "center" }}>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              Target year
            </th>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              Total Year Plan (T-JPY)
            </th>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              Monthly break-down / Change History
            </th>
          </tr>
        </thead>
        <tbody>
          {yearlyPlanningDate.map((item, idx) => {
            const monthlyBreaks = item?.value?.MonthlyJSONData || [];

            return monthlyBreaks.map((yearBlock: any, innerIdx: number) => {
              const entries = Array.isArray(yearBlock?.value)
                ? yearBlock.value
                : [];

              let matchType = "SL4 Payment Plan"; // default for Material
              if (selectedCostCategory === "Outsourcing Labor") {
                matchType = "FTE Budget Plan (T-JPY)";
              }

              const totalForType = entries
                .filter((entry: any) => entry.Type === matchType)
                .reduce(
                  (sum: number, entry: any) =>
                    sum + parseMoney(entry.initialJPY),
                  0
                );

              return (
                <tr key={`${idx}-${innerIdx}`} style={{ textAlign: "center" }}>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    {yearBlock.key}
                  </td>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    {totalForType.toFixed(2)}
                  </td>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    <button
                      onClick={() => handleDepartmentLinkClick(item, yearBlock)}
                      style={{ padding: "5px 10px" }}
                    >
                      Link
                    </button>
                  </td>
                </tr>
              );
            });
          })}
        </tbody>
      </table>
    );
  };
  const handleDepartmentLinkClick = (item: any, yearBlock: any) => {
    if (yearBlock) {
      // loadRowsFromMonthlyJSONData expects an array of such objects,
      // so wrap yearBlock in an array
      loadRowsFromMonthlyJSONData([yearBlock]);
    }

    setSelectedYearBlock(yearBlock);
    setShowYearlyDetailModal(true);
  };
  //END OF DEPARTMENT

  //START LABOUR VALIDATION
  const validateLabourData = (
    outsourceRowData: RowData[][],
    outsourceYears: number[]
  ): string | null => {
    if (!outsourceRowData?.length)
      return "Please add at least one outsource labour year table.";
    if (isEmpty(selectedCostCategory)) return "Please select Cost Category.";
    if (isEmpty(selectedCostTypeId)) return "Please select Cost Type.";
    if (isEmpty(selectedCostDescriptionId))
      return "Please select Cost Description.";
    if (isEmpty(selectedProjectId)) return "Please select Project.";
    if (isEmpty(selectedSubProjectId)) return "Please select Sub Project.";
    if (isEmpty(selectedActivityCategoryId))
      return "Please select Activity Category.";
    if (isEmpty(selectedActivityTypeId)) return "Please select Activity Type.";
    if (isEmpty(selectedFeatureDescriptionId))
      return "Please select Feature Description.";
    if (isEmpty(activityDescription?.trim()))
      return "Please enter Activity Description.";
    if (!outsourceRowData?.length)
      return "Please add at least one outsource labour year table.";

    for (let idx = 0; idx < outsourceRowData.length; idx++) {
      const yearData = outsourceRowData[idx];
      yearData.forEach((row, i) => {});

      const year = outsourceYears[idx] || "unknown";

      // Find the 'FTE Budget Plan (T-JPY)' row with trimmed category name
      const internalLaborPlanRow = yearData.find((row: RowData) => {
        return row.category?.trim() === "FTE Budget Plan (T-JPY)";
      });

      if (!internalLaborPlanRow) {
        return `Missing 'FTE Budget Plan (T-JPY)' row in labor data for year ${year}.`;
      }

      // Check if the months array exists and has exactly 12 months, but allow some missing values
      if (
        !Array.isArray(internalLaborPlanRow.months) ||
        internalLaborPlanRow.months.length !== 12
      ) {
        return `Months array must have 12 entries for 'FTE Budget Plan (T-JPY)' for year ${year}.`;
      }

      // Check that each month is either a valid value or empty
      const invalidMonths = internalLaborPlanRow.months.filter(
        (month: string) => month !== "" && isNaN(parseFloat(month))
      );
      if (invalidMonths.length > 0) {
        return `Invalid month value found in 'FTE Budget Plan (T-JPY)' for year ${year}. Only numbers or empty values are allowed.`;
      }
    }

    return null;
  };

  const handleSaveLabourBudget = () => {
    try {
      const err = validateLabourData(outsourceRowData, outsourceYears);
      if (err) {
        alert(err);
        setErrorMessage(err);
        setErrorPopupVisible(true);
        return;
      }

      const { monthlyPlanningData, monthlyBreakData } =
        buildOutsourceMonthlyArrays(outsourceRowData, outsourceYears);
      const payload = {
        Key: planningNo,
        value: {
          RequestId: 0,
          PlanningNumber: planningNo,
          ActivityCategory: selectedActivityCategoryId || 0,
          ActivityType: selectedActivityTypeId || 0,
          FeatureDescription: selectedFeatureDescriptionId || 0,
          ActivityContent: activityDescription,
          Remarks: "",
          SLA_C4D_x0020_HDEP: "",
          CostCategory: selectedCostCategory,
          CostType: selectedCostTypeId,
          CostDescription: selectedCostDescriptionId,
          projCategory: 0,
          projCluster: 0,
          projName: selectedProjectId || 0,
          subprojName: selectedSubProjectId || 0,
          projectLeaderID: projectLeader || "",
          planApproval: "0",
          MonthlyBreakup: monthlyPlanningData,
          MonthlyJSONData: monthlyBreakData,
        },
      };
      if (
        !payload.value ||
        !Array.isArray(payload.value.MonthlyBreakup) ||
        !Array.isArray(payload.value.MonthlyJSONData)
      ) {
        console.error("Invalid payload:", payload);
        alert("Invalid data format. Check console.");
        return;
      }

      const next = [...(yearlyPlanningDate || []), payload];
      setYearlyPlanningDate(next);
      setYearlyData(next);

      setShowPopup(false);
    } catch (ex) {
      console.error("Error in handleSaveLabourBudget:", ex);
      alert("Unexpected error occurred. Check console for details.");
    }
  };

  //OUTSOURCE TABLE AFTER SAVE
  const buildOutsourceRowsForYear = (
    yearIdx: number,
    outsourceRowData: RowData[][],
    outsourceYears: number[]
  ) => {
    const yearTable = outsourceRowData[yearIdx];

    if (!yearTable) {
      console.warn(`No data found for year index ${yearIdx}`);
      return { rowPlanningData: [], rowbreakupData: [], initialJPY: 0 };
    }

    const rowPlanningData: any[] = [];
    const rowbreakupData: any[] = [];

    yearTable.forEach((row: RowData, idx: number) => {
      const months = (row.months || []).map((m) => parseMoney(m));

      const val = {
        Type: row.category,
        Year: outsourceYears[yearIdx],
        initialJPY: parseMoney(row.yearSummary?.plan || 0),
        fte: row.yearSummary?.fte || 0,
        jan: months[0] || 0,
        feb: months[1] || 0,
        mar: months[2] || 0,
        apr: months[3] || 0,
        may: months[4] || 0,
        jun: months[5] || 0,
        jul: months[6] || 0,
        aug: months[7] || 0,
        sep: months[8] || 0,
        oct: months[9] || 0,
        nov: months[10] || 0,
        dec: months[11] || 0,
        op: parseMoney(row.totals.op),
        ea1: parseMoney(row.totals.ea1),
        ea2: parseMoney(row.totals.ea2),
        yearEnd: parseMoney(row.totals.yearEnd),
        comments: "",
      };

      const year = outsourceYears[yearIdx];
      const key = `${planningNo}_${selectedCostCategory}_${year}_${idx + 1}`;

      rowPlanningData.push({ key, value: val });
      rowbreakupData.push(val);
    });

    return {
      rowPlanningData,
      rowbreakupData,
      initialJPY: rowPlanningData.reduce(
        (sum, r) => sum + parseMoney(r.value.initialJPY),
        0
      ),
    };
  };

  const buildOutsourceMonthlyArrays = (
    outsourceRowData: RowData[][],
    outsourceYears: number[]
  ) => {
    const allPlanningData: any[] = [];
    const allBreakupData: any[] = [];

    outsourceYears.forEach((year, idx) => {
      const { rowPlanningData, rowbreakupData } = buildOutsourceRowsForYear(
        idx,
        outsourceRowData,
        outsourceYears
      );

      allPlanningData.push(...rowPlanningData);

      // 👇 Push grouped data by year
      allBreakupData.push({
        key: year.toString(),
        value: rowbreakupData,
      });
    });

    return {
      monthlyPlanningData: allPlanningData,
      monthlyBreakData: allBreakupData,
    };
  };

  //On LINK CLICK AFTER SAVE
  const [selectedYearBlock, setSelectedYearBlock] = React.useState<any | null>(
    null
  );

  const handleLinkClick = (item: any, yearBlock: any) => {
    if (yearBlock) {
      // loadRowsFromMonthlyJSONData expects an array of such objects,
      // so wrap yearBlock in an array
      loadRowsFromMonthlyJSONData([yearBlock]);
    }

    setSelectedYearBlock(yearBlock);

    // Check the cost category from the item
    const costCategory = item?.value?.CostCategory;

    if (costCategory === "Material") {
      setShowMaterialDetailModal(true);
    } else if (costCategory === "Department") {
      setShowDepartmentDetailModal(true);
    } else if (costCategory === "Outsourcing Labor") {
      setShowOutsourceDetailModal(true);
    } else {
      setShowYearlyDetailModal(true); // fallback
    }
  };

  const handleModalClose = () => {
    setShowYearlyDetailModal(false);
    setShowMaterialDetailModal(false);
    setShowDepartmentDetailModal(false);
    setShowOutsourceDetailModal(false);
    setSelectedYearBlock(null);
  };

  const handleDataUpdate = (updatedYearBlock: any) => {
    setYearlyPlanningDate((prev) =>
      prev.map((item) => {
        // Find the item that contains this year block
        const monthlyJSONData = item.value.MonthlyJSONData || [];
        const hasMatchingYear = monthlyJSONData.some(
          (yearData: any) => yearData.key === updatedYearBlock.key
        );

        if (hasMatchingYear) {
          // Update the specific year's data in MonthlyJSONData
          const updatedMonthlyJSONData = monthlyJSONData.map(
            (yearData: any) => {
              if (yearData.key === updatedYearBlock.key) {
                return updatedYearBlock;
              }
              return yearData;
            }
          );

          return {
            ...item,
            value: {
              ...item.value,
              MonthlyJSONData: updatedMonthlyJSONData,
            },
          };
        }
        return item;
      })
    );
  };

  const renderYearlyPlanSummary = () => {
    return (
      <table
        style={{ borderCollapse: "collapse", width: "100%", marginTop: "20px" }}
      >
        <thead>
          <tr style={{ backgroundColor: "#f0f0f0", textAlign: "center" }}>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              Target year
            </th>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              Total Year Plan (T-JPY)
            </th>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              Monthly break-down / Change History
            </th>
          </tr>
        </thead>
        <tbody>
          {yearlyPlanningDate.map((item, idx) => {
            const monthlyBreaks = item?.value?.MonthlyJSONData || [];

            return monthlyBreaks.map((yearBlock: any, innerIdx: number) => {
              const entries = Array.isArray(yearBlock?.value)
                ? yearBlock.value
                : [];

              let matchType = "SL4 Payment Plan"; // default for Material
              if (selectedCostCategory === "Outsourcing Labor") {
                matchType = "FTE Budget Plan (T-JPY)";
              }

              const totalForType = entries
                .filter((entry: any) => entry.Type === matchType)
                .reduce(
                  (sum: number, entry: any) =>
                    sum + parseMoney(entry.initialJPY),
                  0
                );

              return (
                <tr key={`${idx}-${innerIdx}`} style={{ textAlign: "center" }}>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    {yearBlock.key}
                  </td>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    {totalForType.toFixed(2)}
                  </td>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    <button
                      onClick={() => handleLinkClick(item, yearBlock)}
                      style={{ padding: "5px 10px" }}
                    >
                      Link
                    </button>
                  </td>
                </tr>
              );
            });
          })}
        </tbody>
      </table>
    );
  };

  //Dedlete Functionality
  const [selectedRows, setSelectedRows] = React.useState(new Set<number>()); // track selected rows by index
  // Toggle checkbox selection
  const toggleRowSelection = (index: number) => {
    setSelectedRows((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  // Delete selected rows
  const deleteSelectedRows = () => {
    setYearlyPlanningDate((prev) =>
      prev.filter((_, idx) => !selectedRows.has(idx))
    );
    setSelectedRows(new Set()); // reset selection
  };
  const [viewModeOutsourceYears, setViewModeOutsourceYears] = React.useState<
    number[]
  >([]);
  const [viewModeOutsourceRowData, setViewModeOutsourceRowData] =
    React.useState<RowData[][]>([]);
  const [showViewTable, setShowViewTable] = React.useState(false);

  const renderOutsourceLabourSummary = () => {
    return (
      <table
        style={{ borderCollapse: "collapse", width: "100%", marginTop: "20px" }}
      >
        <thead>
          <tr style={{ backgroundColor: "#f0f0f0", textAlign: "center" }}>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              Target year
            </th>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              Total Year Plan (T-JPY)
            </th>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              Monthly break-down / Change History
            </th>
          </tr>
        </thead>
        <tbody>
          {yearlyPlanningDate.map((item, idx) => {
            const monthlyBreaks = item?.value?.MonthlyJSONData || [];

            return monthlyBreaks.map((yearBlock: any, innerIdx: number) => {
              const entries = Array.isArray(yearBlock?.value)
                ? yearBlock.value
                : [];
              // Get total JPY sum from all rows
              let matchType = "FTE Budget Plan (T-JPY"; // default for Material
              const totalJPY = entries
                .filter((entry: any) => entry.Type === matchType)
                .reduce(
                  (sum: number, entry: any) =>
                    sum + parseMoney(entry.initialJPY),
                  0
                );

              return (
                <tr key={`${idx}-${innerIdx}`} style={{ textAlign: "center" }}>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    {yearBlock.key}
                  </td>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    {totalJPY.toFixed(2)}
                  </td>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    <button
                      onClick={() => handleLabourLinkClick(item, yearBlock)}
                      style={{ padding: "5px 10px" }}
                    >
                      Link
                    </button>
                  </td>
                </tr>
              );
            });
          })}
        </tbody>
      </table>
    );
  };
  const handleLabourLinkClick = (item: any, yearBlock: any) => {
    const year = parseInt(yearBlock.key);
    const entries = Array.isArray(yearBlock.value) ? yearBlock.value : [];

    const rowData = convertYearBlockToRowData(entries);

    setViewModeOutsourceYears([year]); // Only showing one year here
    setViewModeOutsourceRowData([rowData]);
    setShowViewTable(true); // trigger to show the table
  };

  function convertYearBlockToRowData(entries: any[]): RowData[] {
    const categories = [
      "FTE Plan (Numbers)",
      "FTE Budget Plan (T-JPY)",
      "FTE Budget Actual (T-JPY)",
    ];

    return categories.map((cat) => {
      const source = entries.find((e) => e.Type === cat) || {};
      const months = [
        source.jan,
        source.feb,
        source.mar,
        source.apr,
        source.may,
        source.jun,
        source.jul,
        source.aug,
        source.sep,
        source.oct,
        source.nov,
        source.dec,
      ].map((val) => (val !== undefined ? val.toString() : ""));

      return {
        category: cat,
        months,
        totals: {
          op: source.op?.toString() || "",
          ea1: source.ea1?.toString() || "",
          ea2: source.ea2?.toString() || "",
          yearEnd: source.yearEnd?.toString() || "",
        },
        yearSummary: {
          plan: parseFloat(source.initialJPY || "0"),
          fte: parseFloat(source.fte || "0"),
        },
      };
    });
  }

  const handleSave = () => {
    if (selectedCostCategory === "Material") {
      handleSaveMaterialBudget();
    } else if (selectedCostCategory === "Outsourcing Labor") {
      handleSaveLabourBudget();
    } else if (selectedCostCategory === "Internal Labor") {
      handleSaveInternalLaborBudget(); // ✅ Now implemented
    } else {
      setErrorMessage("Unsupported cost category selected.");
      setErrorPopupVisible(true);
    }
  };

  // For internal labour table

  const [internalLabourData, setInternalLabourData] = React.useState<
    LabourYearData[]
  >([]);
  const [selectedInternalYears, setSelectedInternalYears] = React.useState<
    number[]
  >([]);
  const [showIntYearlyDetailModal, setShowIntYearlyDetailModal] =
    React.useState(false);
  const [, setSelectedIntYearBlock] = React.useState<any | null>(null);

  const toggleInternalYearSelection = (year: number) => {
    setSelectedInternalYears((prev) =>
      prev.includes(year) ? prev.filter((y) => y !== year) : [...prev, year]
    );
  };

  const handleInternalLabourDataChange = (updatedData: LabourYearData[]) => {
    setInternalLabourData(updatedData);
  };

  const deleteSelectedInternalYears = () => {
    setInternalLabourData(
      internalLabourData.filter(
        (data) => !selectedInternalYears.includes(data.year)
      )
    );
    setSelectedInternalYears([]);
  };

  const calculateTotalsByQuarter = React.useCallback(
    (months: string[], rate: number): Totals => {
      const monthVals = months
        .map((m) => parseFloat(m) || 0)
        .map((v) => v * rate);
      const curMonth = new Date().getMonth();
      const totalVal = monthVals.reduce((a, b) => a + b, 0);
      let op = "",
        ea1 = "",
        ea2 = "",
        yearEnd = "";
      if (curMonth <= 0) op = totalVal.toFixed(2);
      else if (curMonth < 6) ea1 = totalVal.toFixed(2);
      else if (curMonth < 8) ea2 = totalVal.toFixed(2);
      else yearEnd = totalVal.toFixed(2);
      const result = {
        op,
        ea1,
        ea2,
        yearEnd,
        total: totalVal.toFixed(2),
      };
      console.log("calculateTotalsByQuarter result:", result);
      return result;
    },
    []
  );

  const [, setInternalLabourSaved] = React.useState(false);
  React.useEffect(() => {
    if (selectedCostCategory === "Internal Labor") {
      const currentYear = new Date().getFullYear();

      // If no internal labour data yet, initialize with current year
      if (internalLabourData.length === 0) {
        const initialYearData: LabourYearData = {
          year: currentYear,
          rows: internalLabourCategories.map((category) => ({
            category,
            months: new Array(12).fill(""),
            totals: { op: "", ea1: "", ea2: "", yearEnd: "", total: "" },
            year: currentYear, // ✅ Add this to match your LabourRow type
          })),
        };

        setInternalLabourData([initialYearData]);
        setSelectedInternalYears([currentYear]); // Select current year by default
      }
    } else {
      // Optional: clear internal labour data when switching to another category
      setInternalLabourData([]);
      setSelectedInternalYears([]);
    }
  }, [selectedCostCategory]); // ✅ Complete the dependency array
  const addInternalYear = () => {
    if (internalLabourData.length === 0) return;

    const years = internalLabourData.map((d) => d.year);
    const maxYear = Math.max(...years);
    const nextYear = maxYear + 1;

    if (nextYear > 2035) return;

    const newYearData: LabourYearData = {
      year: nextYear,
      rows: internalLabourCategories.map((category) => ({
        category,
        months: new Array(12).fill(""),
        totals: { op: "", ea1: "", ea2: "", yearEnd: "", total: "" },
        year: nextYear, // ✅ Add this
      })),
    };

    setInternalLabourData([...internalLabourData, newYearData]);
  };
  const handleSaveInternalLaborBudget = () => {
    const err = validateInternalLabourData(internalLabourData);
    if (err) {
      setErrorMessage(err);
      setErrorPopupVisible(true);
      return;
    }

    const { monthlyPlanningData, monthlyBreakData } =
      buildAllMonthlyArraysForLabor();
    console.log("Department Test " + monthlyBreakData, monthlyPlanningData);
    const payload = {
      Key: planningNo,
      value: {
        RequestId: 0,
        PlanningNumber: planningNo,
        MonthlyBreakup: monthlyPlanningData, // ✅ Needed for storage if required elsewhere
        MonthlyJSONData: monthlyBreakData, // ✅ Used by summary table
        LabourDetails: internalLabourData,
        ActivityCategory: selectedActivityCategoryId || 0,
        ActivityType: selectedActivityTypeId || 0,
        FeatureDescription: selectedFeatureDescriptionId || 0,
        ActivityContent: activityDescription,
        Remarks: "", // Wire this to your remark state if you have one
        SLA_C4D_x0020_HDEP: "", // Wire this to your SLA state
        CostCategory: selectedCostCategory,
        CostType: selectedCostTypeId,
        CostDescription: selectedCostDescriptionId,
        projCategory: 0,
        projCluster: 0,
        projName: selectedProjectId || 0,
        subprojName: selectedSubProjectId || 0,
        projectLeaderID: projectLeader || "",
        planApproval: "0",
        //MonthlyBreakup: monthlyPlanningData,
        //MonthlyJSONData: monthlyBreakData,
      },
    };

    setYearlyPlanningDate((prev) => {
      const next = [...prev, payload];
      setYearlyData(next);
      return next;
    });

    setShowPopup(false);
    setInternalLabourSaved(true);
  };
  const validateInternalLabourData = (labourData: any[]): string | null => {
    if (isEmpty(selectedCostCategory)) return "Please select Cost Category.";
    if (isEmpty(selectedCostTypeId)) return "Please select Cost Type.";
    if (isEmpty(selectedCostDescriptionId))
      return "Please select Cost Description.";
    if (isEmpty(selectedProjectId)) return "Please select Project.";
    if (isEmpty(selectedSubProjectId)) return "Please select Sub Project.";
    if (isEmpty(selectedActivityCategoryId))
      return "Please select Activity Category.";
    if (isEmpty(selectedActivityTypeId)) return "Please select Activity Type.";
    if (isEmpty(selectedFeatureDescriptionId))
      return "Please select Feature Description.";
    if (isEmpty(activityDescription?.trim()))
      return "Please enter Activity Description.";

    if (!labourData?.length) return "Please add at least one year table.";

    if (!rowData?.length) return "Please add at least one year table.";

    for (const y of internalLabourData) {
      // Find the row with spendingLevel "Internal Labor Plan (hrs)"
      const internalLabor = y.rows.find(
        (r: any) => r.category === "Labor Plan (hrs)"
      );

      // Assuming totals.total is a string representing a number, parse it
      const total = parseMoney(internalLabor?.totals?.total || "");

      if (total <= 0) {
        return `Enter monthly values so that 'Labor Plan (hrs)' has a total for year ${y.year}.`;
      }
    }
    return null;
  };
  function buildAllMonthlyArraysForLabor() {
    const monthlyPlanningData: any[] = [];
    const monthlyBreakData: any[] = [];

    internalLabourData.forEach((yearData) => {
      const { year, rows } = yearData;

      const monthlyEntriesForYear: any[] = [];

      rows.forEach((row) => {
        const type = row.category;

        row.months.forEach((val, idx) => {
          const month = getMonthName(idx); // e.g., 0 -> "Jan"
          if (val && val.trim() !== "") {
            monthlyEntriesForYear.push({
              Month: month,
              Type: type,
              initialJPY: parseFloat(val),
            });
          }
        });
      });

      // Push to the final collections
      monthlyPlanningData.push(...monthlyEntriesForYear);

      monthlyBreakData.push({
        key: year.toString(),
        value: monthlyEntriesForYear,
      });
    });

    return { monthlyPlanningData, monthlyBreakData };
  }
  function getMonthName(index: number): string {
    const months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    return months[index] || "";
  }
  const renderinternalPlanSummary = () => {
    return (
      <table
        style={{ borderCollapse: "collapse", width: "100%", marginTop: "20px" }}
      >
        <thead>
          <tr style={{ backgroundColor: "#f0f0f0", textAlign: "center" }}>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              Target year
            </th>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              {selectedCostCategory === "Internal Labor"
                ? "Total Year Plan (hrs)"
                : "Total Year Plan (T-JPY)"}
            </th>
            <th style={{ padding: "10px", border: "1px solid #ccc" }}>
              Monthly break-down
            </th>
          </tr>
        </thead>
        <tbody>
          {yearlyPlanningDate.map((item, idx) => {
            const monthlyBreaks = item?.value?.MonthlyJSONData || [];

            return monthlyBreaks.map((yearBlock: any, innerIdx: number) => {
              const entries = Array.isArray(yearBlock?.value)
                ? yearBlock.value
                : [];

              let matchType = "Labor Plan (hrs)";
              if (selectedCostCategory === "Internal Labor") {
                matchType = "Labor Plan (hrs)";
              }

              const totalForType = entries
                .filter((entry: any) => entry.Type === matchType)
                .reduce(
                  (sum: number, entry: any) =>
                    sum + (Number(entry.initialJPY) || 0),
                  0
                );

              return (
                <tr key={`${idx}-${innerIdx}`} style={{ textAlign: "center" }}>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    {yearBlock.key}
                  </td>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    {!isNaN(totalForType) ? totalForType.toFixed(2) : "0.00"}
                  </td>
                  <td style={{ padding: "10px", border: "1px solid #ccc" }}>
                    <button
                      onClick={() => handleinternalLinkClick(item, yearBlock)}
                      style={{ padding: "5px 10px" }}
                    >
                      Link
                    </button>
                  </td>
                </tr>
              );
            });
          })}
        </tbody>
      </table>
    );
  };

  const handleinternalLinkClick = (item: any, yearBlock: any) => {
    if (!yearBlock) return;

    const yearKey = parseInt(yearBlock.key, 10);
    const currentYearData = internalLabourData.find((d) => d.year === yearKey);

    if (currentYearData) {
      setSelectedIntYearBlock(currentYearData);
      setShowIntYearlyDetailModal(true);
    } else {
      setSelectedIntYearBlock(yearBlock);
      setShowIntYearlyDetailModal(true);
    }
  };

  return (
    <>
      <AllPlanningWp />
      <div className={styles.budgetForm}>
        <div className={styles.buttonGroup}>
          <button
            className={
              activeButton === "project" ? styles.active : styles.inactive
            }
            onClick={() => setActiveButton("project")}
          >
            <Icon iconName="ProjectCollection" style={{ marginRight: 8 }} />
            Project Budget
          </button>
          <button
            className={
              activeButton === "department" ? styles.active : styles.inactive
            }
            onClick={() => setActiveButton("department")}
          >
            <Icon iconName="Group" style={{ marginRight: 8 }} />
            Department Budget
          </button>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label>Requestor Name</label>
            <input type="text" value={userDisplayName} readOnly />
          </div>

          <div className={styles.formGroup}>
            <label>L3 Department</label>
            <select
              value={l3SelectedId}
              onChange={(e) => setL3SelectedId(Number(e.target.value))}
            >
              <option value="">Select L3 Department</option>
              {l3Options.map((opt) => (
                <option key={opt.Id} value={opt.Id}>
                  {opt.Title}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.formGroup}>
            <label>L4 Department</label>
            <select
              value={l4SelectedId}
              onChange={(e) => setL4SelectedId(Number(e.target.value))}
            >
              <option value="">Select L4 Department</option>
              {l4Options.map((opt) => (
                <option key={opt.Id} value={opt.Id}>
                  {opt.Title}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
      <div style={{ overflowX: "auto", width: "100%" }}>
        <table
          style={{
            width: "100%",
            borderCollapse: "collapse",
            minWidth: "1200px",
          }}
        >
          <thead>
            <tr style={{ backgroundColor: "#ddd" }}>
              <th style={{ border: "1px solid #ccc" }}>
                {/* Empty header for checkbox column */}
              </th>
              <th style={{ border: "1px solid #ccc", color: "#0070C0" }}>
                Project Name
              </th>
              <th style={{ border: "1px solid #ccc", color: "#0070C0" }}>
                SubProject Name
              </th>
              <th style={{ border: "1px solid #ccc", color: "#0070C0" }}>
                Cost Category
              </th>
              <th style={{ border: "1px solid #ccc", color: "#0070C0" }}>
                Cost Type
              </th>
              <th style={{ border: "1px solid #ccc", color: "#0070C0" }}>
                Cost Description
              </th>
              <th style={{ border: "1px solid #ccc", color: "#0070C0" }}>
                Activity Category
              </th>
              <th style={{ border: "1px solid #ccc", color: "#0070C0" }}>
                Activity Type
              </th>
              <th style={{ border: "1px solid #ccc", color: "#0070C0" }}>
                Feature Description
              </th>
              <th style={{ border: "1px solid #ccc", color: "#0070C0" }}>
                Activity Content
              </th>
              <th style={{ border: "1px solid #ccc", color: "#0070C0" }}>
                Budget Yearly Plan
              </th>
            </tr>
          </thead>
          <tbody>
            {yearlyPlanningDate.map((item, index) => {
              const value = item.value;
              const projNameTitle =
                projectOptions.find((p) => p.Id === value.projName)?.Title ||
                value.projName ||
                "NA";
              const subProjTitle =
                subProjectOptions.find((s) => s.Id === value.subprojName)
                  ?.Title ||
                value.subprojName ||
                "NA";
              const costCategoryTitle = costCategoryOptions.includes(
                value.CostCategory
              )
                ? value.CostCategory
                : "NA";
              const costTypeTitle =
                costTypeOptions.find((c) => c.Id === value.CostType)?.Title ||
                value.CostType ||
                "NA";
              const costDescriptionTitle =
                costDescriptionOptions.find(
                  (c) => c.Id === value.CostDescription
                )?.Title ||
                value.CostDescription ||
                "NA";
              const activityCategoryTitle = (() => {
                const match = activityCategories.find(
                  (a) => a.Id === value.ActivityCategory
                );
                if (!value.ActivityCategory || !match) return "NA";
                return match.Title;
              })();
              const activityTypeTitle = (() => {
                const match = activityTypes.find(
                  (a) => a.id === value.ActivityType
                );
                if (!value.ActivityType || !match) return "NA";
                return match.label;
              })();

              const featureDescriptionTitle = (() => {
                const match = featureDescriptions.find(
                  (f) => f.id === value.FeatureDescription
                );
                if (!value.FeatureDescription || !match) return "NA";
                return match.label;
              })();

              return (
                <tr key={index}>
                  <td style={{ textAlign: "center", border: "1px solid #ccc" }}>
                    <input
                      type="checkbox"
                      checked={selectedRows.has(index)}
                      onChange={() => toggleRowSelection(index)}
                    />
                  </td>
                  <td>{projNameTitle}</td>
                  <td>{subProjTitle}</td>
                  <td>{costCategoryTitle}</td>
                  <td>{costTypeTitle}</td>
                  <td>{costDescriptionTitle}</td>
                  <td>{activityCategoryTitle}</td>
                  <td>{activityTypeTitle}</td>
                  <td>{featureDescriptionTitle}</td>
                  <td>{value.ActivityContent ?? "-"}</td>
                  <td>
                    {selectedCostCategory === "Outsourcing Labor"
                      ? renderOutsourceLabourSummary()
                      : selectedCostCategory === "Material"
                      ? renderYearlyPlanSummary()
                      : selectedCostCategory === "Internal Labor"
                      ? renderinternalPlanSummary()
                      : selectedCostCategory === "Department"
                      ? renderDepartmentYearlyPlanSummary()
                      : null}

                    {showYearlyDetailModal && selectedYearBlock && (
                      <YearlyDetailModal
                        yearBlock={selectedYearBlock}
                        onClose={handleModalClose}
                        spendingLevels={spendingLevels}
                      />
                    )}
                    {showMaterialDetailModal && selectedYearBlock && (
                      <MaterialDetailModal
                        yearBlock={selectedYearBlock}
                        onClose={handleModalClose}
                        spendingLevels={spendingLevels}
                        onDataUpdate={handleDataUpdate}
                      />
                    )}
                    {showDepartmentDetailModal && selectedYearBlock && (
                      <DepartmentDetailModal
                        yearBlock={selectedYearBlock}
                        onClose={handleModalClose}
                        spendingLevels={spendingLevels}
                        onDataUpdate={handleDataUpdate}
                      />
                    )}
                    {showOutsourceDetailModal && selectedYearBlock && (
                      <OutsourceDetailModal
                        yearBlock={selectedYearBlock}
                        onClose={handleModalClose}
                        onDataUpdate={handleDataUpdate}
                      />
                    )}
                    {showViewTable && (
                      <Modal onClose={() => setShowViewTable(false)}>
                        <OutsourceLabourTable
                          outsourceYears={viewModeOutsourceYears}
                          selectedOutsourceYears={[]}
                          outsourceRowData={viewModeOutsourceRowData}
                          addOutsourceTable={() => {}}
                          deleteSelectedOutsourceTables={() => {}}
                          toggleOutsourceYear={() => {}}
                          handleOutsourceMonthChange={() => {}}
                        />
                      </Modal>
                    )}
                    {showIntYearlyDetailModal && (
                      <Modal onClose={() => setShowIntYearlyDetailModal(false)}>
                        <InternalLabourTable
                          labourData={internalLabourData}
                          onDataChange={handleInternalLabourDataChange}
                          selectedYears={selectedInternalYears}
                          toggleYearSelection={toggleInternalYearSelection}
                          addYear={addInternalYear}
                          deleteYears={deleteSelectedInternalYears}
                          calculateTotalsByQuarter={calculateTotalsByQuarter}
                          department={Department}
                          isModal={true}
                          onUpdate={() => {
                            // Handle update logic, e.g., save changes and close modal
                            handleSaveInternalLaborBudget();
                            setShowIntYearlyDetailModal(false);
                          }}
                          onCancel={() => {
                            // Just close modal without saving
                            setShowIntYearlyDetailModal(false);
                          }}
                        />
                      </Modal>
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>

          <tbody></tbody>
        </table>
        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            marginTop: "10px",
          }}
        >
          <button
            style={addButtonStyle}
            onClick={() => {
              if (!l3SelectedId) {
                setErrorMessage("Please select L3 departments.");
                setErrorPopupVisible(true);
                return;
              }
              if (!l4SelectedId) {
                setErrorMessage("Please select L4 departments.");
                setErrorPopupVisible(true);
                return;
              }
              if (activeButton === "project") {
                setShowPopup(true);
              } else if (activeButton === "department") {
                setShowDepartmentPopup(true);
              }
            }}
          >
            Add
          </button>
          <button
            style={deleteButtonStyle}
            onClick={deleteSelectedRows}
            disabled={selectedRows.size === 0}
          >
            Delete
          </button>
        </div>
      </div>
      {renderPopupForm()}
      {renderDepartmentPopupForm()}
      {renderErrorPopup()}
    </>
  );
};

export default NewPlanningWp;
