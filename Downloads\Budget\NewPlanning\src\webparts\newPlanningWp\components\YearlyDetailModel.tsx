import React from "react";
import DepartmentTable from "./DepartmentTable";



interface YearlyEntry {
  Type: string;
  jan?: string | number;
  feb?: string | number;
  mar?: string | number;
  apr?: string | number;
  may?: string | number;
  jun?: string | number;
  jul?: string | number;
  aug?: string | number;
  sep?: string | number;
  oct?: string | number;
  nov?: string | number;
  dec?: string | number;
  op?: string | number;
  ea1?: string | number;
  ea2?: string | number;
  yearEnd?: string | number;
  initialJPY?: string | number;
   [key: string]: number | string | undefined;
}

interface YearBlock {
  key: string | number;
  value: YearlyEntry[];
}


const overlayStyles: React.CSSProperties = {
  position: "fixed",
  top: 0,
  left: 0,
  width: "100vw",
  height: "100vh",
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  zIndex: 9999,
};

const modalStyles: React.CSSProperties = {
  backgroundColor: "#fff",
  padding: "20px",
  borderRadius: "8px",
  width: "80%",
  maxHeight: "80vh",
  overflowY: "auto",
  boxShadow: "0 2px 10px rgba(0,0,0,0.2)"
};

interface Totals {
  op: string;
  ea1: string;
  ea2: string;
  yearEnd: string;
  total: string;
}

interface EditableRow {
  spendingLevel: string;
  months: string[];
  totals: Totals;
}

interface EditableYearData {
  year: number;
  rows: EditableRow[];
}
interface YearlyDetailModalProps {
  yearBlock: YearBlock;
  onClose: () => void;
  spendingLevels: string[];
}

const YearlyDetailModal: React.FC<YearlyDetailModalProps> = ({ yearBlock, onClose, spendingLevels }) => {
const [editableData, setEditableData] = React.useState<EditableYearData[]>([]);

 React.useEffect(() => {
    if (yearBlock) {
      const monthOrder = [
        "jan", "feb", "mar", "apr", "may", "jun",
        "jul", "aug", "sep", "oct", "nov", "dec"
      ];

      const initialData: EditableYearData[] = [
        {
          year: Number(yearBlock.key),
          rows: yearBlock.value.map((entry: YearlyEntry) => {
            const months = monthOrder.map((m) => {
              const val = entry[m as keyof YearlyEntry];
              return val !== undefined && val !== null && val !== 0 ? String(val) : "";
            });

            return {
              spendingLevel: entry.Type,
              months,
              totals: {
                op: String(entry.op ?? ""),
                ea1: String(entry.ea1 ?? ""),
                ea2: String(entry.ea2 ?? ""),
                yearEnd: String(entry.yearEnd ?? ""),
                total: String(entry.initialJPY ?? ""),
              },
            };
          }),
        },
      ];

      setEditableData(initialData);
    }
  }, [yearBlock]);

  const handleMonthChange = (yearIdx: number, rowIdx: number, monthIdx: number, value: string):void => {
    setEditableData((prev) =>
      prev.map((yearData, yIdx) => {
        if (yIdx !== yearIdx) return yearData;

         const updatedRows = yearData.rows.map((row, rIdx) => {
          if (rIdx !== rowIdx) return row;

          const updatedMonths = [...row.months];
          updatedMonths[monthIdx] = value;

          return {
            ...row,
            months: updatedMonths,
          };
        });

        return {
          ...yearData,
          rows: updatedRows,
        };
      })
    );
  };
  return (
    <div style={overlayStyles}>
      <div style={modalStyles}>
        <h3>Yearly Plan Detail for {yearBlock.key}</h3>

        <DepartmentTable
          index={0}
          tblCounter={1}
          curYear={Number(yearBlock.key)}
          createDepartmentTable={() => {}}
          deleteSelectedTables={() => {}}
          selectedYears={[]}
          toggleYearSelection={() => {}}
          tableYears={[Number(yearBlock.key)]}
          rowData={editableData}
          spendingLevels={spendingLevels}
          editableLevels={new Set(spendingLevels.filter(level => level.includes("Plan")))}
          handleMonthChange={handleMonthChange}
        />

        <div style={{ marginTop: "20px", textAlign: "right" }}>
          <button onClick={onClose}
          style={{
            position: "absolute",
            top: "10px",
            right: "10px",
            backgroundColor: "#a00",
            color: "#fff",
            border: "none",
            padding: "6px 12px",
            borderRadius: "4px",
            cursor: "pointer",
            zIndex: 1,
          }}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default YearlyDetailModal;