var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
import * as React from 'react';
import styles from './NewPlanningWp.module.scss';
import { sp } from "@pnp/sp/presets/all";
import "@pnp/sp/webs";
import "@pnp/sp/lists";
import "@pnp/sp/items";
import AllPlanningWp from './AllPlanning';
import { Icon } from '@fluentui/react';
import MaterialTable from './Material';
import DepartmentTable from './DepartmentTable';
import YearlyDetailModal from './YearlyDetailModel';
//import LabourTable from './LabourTable';
import OutsourceLabourTable from './RenderOutsourceTable';
import useOutsourceLabour from "./useOutsourceLabour";
//import{LabourRowData} from "./LabourTable"
import InternalLabourTable from "./InternalLabourTable";
sp.setup({
    sp: {
        baseUrl: "https://corptb.sharepoint.com/sites/apac-10747/"
    }
});
export var internalLabourCategories = [
    "Labor Plan (hrs)",
    "Labor Actual (hrs)",
    "Labor Plan (T-JPY)",
    "Labor Actual (T-JPY)"
];
var NewPlanningWp = function (_a) {
    var userDisplayName = _a.userDisplayName, initialPlanningNo = _a.initialPlanningNo;
    var curYear = new Date().getFullYear(); // or any other assignment you want
    var _b = useOutsourceLabour(curYear), outsourceYears = _b.outsourceYears, selectedOutsourceYears = _b.selectedOutsourceYears, outsourceRowData = _b.outsourceRowData, addOutsourceTable = _b.addOutsourceTable, deleteSelectedOutsourceTables = _b.deleteSelectedOutsourceTables, toggleOutsourceYear = _b.toggleOutsourceYear, handleOutsourceMonthChange = _b.handleOutsourceMonthChange;
    var Modal = function (_a) {
        var children = _a.children, onClose = _a.onClose;
        return (React.createElement("div", { style: {
                position: "fixed",
                top: 0, left: 0,
                width: "100%",
                height: "100%",
                backgroundColor: "rgba(0,0,0,0.5)",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                zIndex: 9999
            } },
            React.createElement("div", { style: {
                    backgroundColor: "white",
                    padding: "20px",
                    borderRadius: "8px",
                    maxHeight: "90%",
                    overflowY: "auto",
                    width: "90%"
                } },
                React.createElement("div", { style: { textAlign: "right" } },
                    React.createElement("button", { onClick: onClose, style: { padding: "6px 12px", backgroundColor: "#a00", color: "#fff" } }, "Close")),
                children)));
    };
    var _c = React.useState([]), l3Options = _c[0], setL3Options = _c[1];
    var _d = React.useState([]), l4Options = _d[0], setL4Options = _d[1];
    var _e = React.useState(''), l3SelectedId = _e[0], setL3SelectedId = _e[1];
    var _f = React.useState(''), l4SelectedId = _f[0], setL4SelectedId = _f[1];
    var _g = React.useState('project'), activeButton = _g[0], setActiveButton = _g[1];
    var _h = React.useState(false), showPopup = _h[0], setShowPopup = _h[1];
    var _j = React.useState(false), showDepartmentPopup = _j[0], setShowDepartmentPopup = _j[1];
    var _k = React.useState(false), errorPopupVisible = _k[0], setErrorPopupVisible = _k[1];
    var _l = React.useState(''), errorMessage = _l[0], setErrorMessage = _l[1];
    var _m = React.useState(false), textareaFocus = _m[0], setTextareaFocus = _m[1];
    var _o = React.useState([]), costActivityData = _o[0], setCostActivityData = _o[1];
    var _p = React.useState([]), costCategoryOptions = _p[0], setCostCategoryOptions = _p[1];
    var _q = React.useState(''), selectedCostCategory = _q[0], setSelectedCostCategory = _q[1];
    var _r = React.useState(""), selectedCostTypeId = _r[0], setSelectedCostTypeId = _r[1];
    var _s = React.useState(""), selectedCostDescriptionId = _s[0], setSelectedCostDescriptionId = _s[1];
    var _t = React.useState([]), costTypeOptions = _t[0], setCostTypeOptions = _t[1];
    var _u = React.useState([]), costDescriptionOptions = _u[0], setCostDescriptionOptions = _u[1];
    var _v = React.useState([]), projectData = _v[0], setProjectData = _v[1];
    var _w = React.useState([]), projectOptions = _w[0], setProjectOptions = _w[1];
    var _x = React.useState([]), subProjectOptions = _x[0], setSubProjectOptions = _x[1];
    var _y = React.useState(""), selectedProjectId = _y[0], setSelectedProjectId = _y[1];
    var _z = React.useState(""), selectedSubProjectId = _z[0], setSelectedSubProjectId = _z[1];
    var _0 = React.useState(''), projectLeader = _0[0], setProjectLeader = _0[1];
    var _1 = React.useState(""), activityDescription = _1[0], setActivityDescription = _1[1];
    var _2 = React.useState(""), Department = _2[0], setDepartment = _2[1];
    var addButtonStyle = {
        backgroundColor: 'green',
        color: 'white',
        padding: '6px 12px',
        border: 'none',
        borderRadius: '3px',
        marginRight: '8px',
        cursor: 'pointer'
    };
    var deleteButtonStyle = {
        backgroundColor: 'red',
        color: 'white',
        padding: '6px 12px',
        border: 'none',
        borderRadius: '3px',
        cursor: 'pointer'
    };
    var modalBackdropStyle = {
        position: 'fixed',
        top: 0, left: 0, right: 0, bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.3)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000
    };
    var modalStyle = {
        width: '80%', // Or a fixed width like '1000px' if needed
        maxHeight: '90vh',
        overflowX: 'auto', // Enable horizontal scroll
        overflowY: 'auto', // Also allow vertical scroll
        backgroundColor: '#fff',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
    };
    var errorModalStyle = {
        backgroundColor: 'white',
        padding: '30px',
        borderRadius: '8px',
        maxWidth: '400px',
        width: '100%',
        textAlign: 'center',
        boxShadow: '0 2px 10px rgba(0,0,0,0.3)'
    };
    var formGridStyle = {
        display: 'grid',
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: '16px',
        marginTop: '16px'
    };
    var okButtonStyle = {
        backgroundColor: '#0078d4',
        color: 'white',
        padding: '12px',
        marginRight: '8px',
        border: 'none',
        borderRadius: '3px',
        cursor: 'pointer',
        width: '10%',
    };
    var cancelButtonStyle = {
        backgroundColor: '#d9534f',
        color: 'white',
        padding: '6px',
        border: 'none',
        borderRadius: '3px',
        cursor: 'pointer',
        width: '10%',
    };
    var textareaStyle = {
        width: '85%',
        padding: '8px',
        resize: 'vertical',
        fontSize: '14px',
        borderRadius: '4px',
        border: '1px solid #ccc',
        outline: 'none',
        borderColor: textareaFocus ? '#0078d4' : '#ccc', // Blue on focus
    };
    //Get Department
    React.useEffect(function () {
        var fetchDepartments = function () { return __awaiter(void 0, void 0, void 0, function () {
            var currentUser, currentUserId, items, l3Id_1, l4Id_1, _a, l3Data, l4Data, filteredL3Options, filteredL4Options, error_1;
            var _b, _c;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        _d.trys.push([0, 4, , 5]);
                        return [4 /*yield*/, sp.web.currentUser.get()];
                    case 1:
                        currentUser = _d.sent();
                        currentUserId = currentUser.Id;
                        return [4 /*yield*/, sp.web.lists
                                .getByTitle("User Registration")
                                .items
                                .select("L3_x0020_Department_x0020_Code/Id", "L4_x0020_Cost_x0020_Block/Id")
                                .expand("L3_x0020_Department_x0020_Code", "L4_x0020_Cost_x0020_Block")
                                .filter("Requestor/Id eq ".concat(currentUserId))
                                .top(1)
                                .get()];
                    case 2:
                        items = _d.sent();
                        l3Id_1 = (_b = items[0].L3_x0020_Department_x0020_Code) === null || _b === void 0 ? void 0 : _b.Id;
                        l4Id_1 = (_c = items[0].L4_x0020_Cost_x0020_Block) === null || _c === void 0 ? void 0 : _c.Id;
                        return [4 /*yield*/, Promise.all([
                                sp.web.lists.getByTitle("L3 Departments").items.select("Id", "Title").top(100).get(),
                                sp.web.lists.getByTitle("L4 Department").items.select("Id", "Title").top(100).get()
                            ])];
                    case 3:
                        _a = _d.sent(), l3Data = _a[0], l4Data = _a[1];
                        filteredL3Options = l3Data.filter(function (dep) { return dep.Id === l3Id_1; });
                        filteredL4Options = l4Data.filter(function (dep) { return dep.Id === l4Id_1; });
                        if (filteredL3Options.length > 0) {
                            setDepartment(filteredL3Options[0].Title); // Set department name here
                        }
                        else {
                            setDepartment("");
                        }
                        setL3Options(filteredL3Options);
                        setL4Options(filteredL4Options);
                        return [3 /*break*/, 5];
                    case 4:
                        error_1 = _d.sent();
                        console.error("Error fetching department data:", error_1);
                        return [3 /*break*/, 5];
                    case 5: return [2 /*return*/];
                }
            });
        }); };
        fetchDepartments();
    }, [userDisplayName]);
    //CostActivity
    React.useEffect(function () {
        var fetchCostActivityData = function () { return __awaiter(void 0, void 0, void 0, function () {
            var response, uniqueCategories, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, sp.web.lists
                                .getByTitle("Cost_Activity_Information")
                                .items
                                .select("Cost_x0020_Category", "Activity_x0020_Category/Title", "Activity_x0020_Type/Title", "Cost_x0020_Type/Title", "Cost_x0020_Description/Title", "Cost_x0020_Type/Id", "Cost_x0020_Description/Id", "Feature_x0020_Description/Title")
                                .expand("Feature_x0020_Description", "Activity_x0020_Category", "Activity_x0020_Type", "Cost_x0020_Description", "Cost_x0020_Type")
                                .orderBy("Cost_x0020_Type/Title", true)
                                .top(4999)
                                .get()];
                    case 1:
                        response = _a.sent();
                        uniqueCategories = Array.from(new Set(response.map(function (item) { return item.Cost_x0020_Category; }))).filter(Boolean);
                        //setCostActivityData(response);
                        setCostActivityData(response);
                        setCostCategoryOptions(uniqueCategories);
                        return [3 /*break*/, 3];
                    case 2:
                        error_2 = _a.sent();
                        console.error("Error fetching Cost Activity data:", error_2);
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        }); };
        fetchCostActivityData();
    }, []);
    //PRoject NAme
    React.useEffect(function () {
        var fetchProjects = function () { return __awaiter(void 0, void 0, void 0, function () {
            var response, uniqueProjects, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, sp.web.lists
                                .getByTitle("ProjectList")
                                .items
                                .select("ProjectName/Title", "ProjectName/Id", "SubProjectName/Title", "SubProjectName/Id")
                                .expand("ProjectName", "SubProjectName")
                                .top(4999)
                                .get()];
                    case 1:
                        response = _a.sent();
                        setProjectData(response);
                        uniqueProjects = Array.from(new Map(response.map(function (item) { var _a; return [(_a = item.ProjectName) === null || _a === void 0 ? void 0 : _a.Id, item.ProjectName]; })).values());
                        uniqueProjects.sort(function (a, b) { return a.Title.localeCompare(b.Title); });
                        setProjectOptions(uniqueProjects);
                        return [3 /*break*/, 3];
                    case 2:
                        error_3 = _a.sent();
                        console.error("Error fetching ProjectList data:", error_3);
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        }); };
        fetchProjects();
    }, []);
    //Cost Category
    React.useEffect(function () {
        if (selectedCostCategory) {
            var filtered = costActivityData.filter(function (item) { return item.Cost_x0020_Category === selectedCostCategory; });
            var uniqueTypes = Array.from(new Map(filtered.map(function (item) { var _a; return [(_a = item.Cost_x0020_Type) === null || _a === void 0 ? void 0 : _a.Id, item.Cost_x0020_Type]; })).values());
            setCostTypeOptions(uniqueTypes);
            setSelectedCostTypeId(""); // Reset selection
            setCostDescriptionOptions([]);
            setSelectedCostDescriptionId("");
        }
        else {
            setCostTypeOptions([]);
            setSelectedCostTypeId("");
            setCostDescriptionOptions([]);
            setSelectedCostDescriptionId("");
        }
    }, [selectedCostCategory]);
    React.useEffect(function () {
        if (selectedCostTypeId) {
            var filtered = costActivityData.filter(function (item) { var _a; return ((_a = item.Cost_x0020_Type) === null || _a === void 0 ? void 0 : _a.Id) === selectedCostTypeId; });
            var uniqueDescriptions = Array.from(new Map(filtered.map(function (item) { var _a; return [(_a = item.Cost_x0020_Description) === null || _a === void 0 ? void 0 : _a.Id, item.Cost_x0020_Description]; })).values());
            setCostDescriptionOptions(uniqueDescriptions);
            setSelectedCostDescriptionId("");
        }
        else {
            setCostDescriptionOptions([]);
            setSelectedCostDescriptionId("");
        }
    }, [selectedCostTypeId]);
    React.useEffect(function () {
        var _a;
        if (selectedProjectId) {
            var filtered = projectData.filter(function (item) { var _a; return ((_a = item.ProjectName) === null || _a === void 0 ? void 0 : _a.Id) === selectedProjectId; });
            var uniqueSubs = Array.from(new Map(filtered.map(function (item) { var _a; return [(_a = item.SubProjectName) === null || _a === void 0 ? void 0 : _a.Id, item.SubProjectName]; })).values());
            uniqueSubs.sort(function (a, b) { return a.Title.localeCompare(b.Title); });
            setSubProjectOptions(uniqueSubs);
            setSelectedSubProjectId("");
            // Set Project Leader from the first matching item
            if (filtered.length > 0 && ((_a = filtered[0].ProjectLeader) === null || _a === void 0 ? void 0 : _a.Title)) {
                setProjectLeader(filtered[0].ProjectLeader.Title);
            }
            else {
                setProjectLeader('');
            }
        }
        else {
            setSubProjectOptions([]);
            setSelectedSubProjectId("");
            setProjectLeader('');
        }
    }, [selectedProjectId, projectData]);
    var _3 = React.useState([]), activityCategories = _3[0], setActivityCategories = _3[1];
    var _4 = React.useState(''), selectedActivityCategoryId = _4[0], setSelectedActivityCategoryId = _4[1];
    React.useEffect(function () {
        var fetchActivityCategories = function () { return __awaiter(void 0, void 0, void 0, function () {
            var items, options, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, sp.web.lists
                                .getByTitle("Activity Category")
                                .items
                                .select("Id", "Title")
                                .top(4999)
                                .orderBy("Title", true)
                                .get()];
                    case 1:
                        items = _a.sent();
                        options = __spreadArray([{ Id: 0, Title: "Select Category" }], items, true);
                        setActivityCategories(options);
                        return [3 /*break*/, 3];
                    case 2:
                        error_4 = _a.sent();
                        console.error("Error fetching activity categories:", error_4);
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        }); };
        fetchActivityCategories();
    }, []);
    var _5 = React.useState([]), activityTypes = _5[0], setActivityTypes = _5[1];
    var _6 = React.useState(0), selectedActivityTypeId = _6[0], setSelectedActivityTypeId = _6[1];
    React.useEffect(function () {
        var fetchActivityTypes = function () { return __awaiter(void 0, void 0, void 0, function () {
            var items, mapped, error_5;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, sp.web.lists
                                .getByTitle("Activity Type")
                                .items
                                .select("Id", "Title")
                                .top(4999)
                                .orderBy("Title", true)
                                .get()];
                    case 1:
                        items = _a.sent();
                        if (items.length > 0) {
                            mapped = __spreadArray([
                                { id: 0, label: "Select Type" }
                            ], items.map(function (item) { return ({
                                id: item.Id,
                                label: item.Title
                            }); }), true);
                            setActivityTypes(mapped);
                            setSelectedActivityTypeId(0); // set default value
                        }
                        else {
                            alert("No Activity Types found.");
                        }
                        return [3 /*break*/, 3];
                    case 2:
                        error_5 = _a.sent();
                        console.error("Error fetching Activity Types: ", error_5);
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        }); };
        fetchActivityTypes();
    }, []);
    var _7 = React.useState([]), featureDescriptions = _7[0], setFeatureDescriptions = _7[1];
    var _8 = React.useState(0), selectedFeatureDescriptionId = _8[0], setSelectedFeatureDescriptionId = _8[1];
    React.useEffect(function () {
        var fetchFeatureDescriptions = function () { return __awaiter(void 0, void 0, void 0, function () {
            var items, mapped, error_6;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, sp.web.lists
                                .getByTitle("Feature Description")
                                .items
                                .select("Id", "Title")
                                .top(4999)
                                .orderBy("Title", true)
                                .get()];
                    case 1:
                        items = _a.sent();
                        if (items.length > 0) {
                            mapped = __spreadArray([
                                { id: 0, label: "Select Feature Description" }
                            ], items.map(function (item) { return ({
                                id: item.Id,
                                label: item.Title
                            }); }), true);
                            setFeatureDescriptions(mapped);
                            setSelectedFeatureDescriptionId(0); // Default selected
                        }
                        else {
                            alert("No Feature Descriptions found.");
                        }
                        return [3 /*break*/, 3];
                    case 2:
                        error_6 = _a.sent();
                        console.error("Error fetching Feature Descriptions: ", error_6);
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        }); };
        fetchFeatureDescriptions();
    }, []);
    var _9 = React.useState(0), setMaterialIndex = _9[1];
    var _10 = React.useState(0), setDepartmentIndex = _10[1];
    var _11 = React.useState(0), setOutsourceIndex = _11[1];
    var _12 = React.useState(0), setLabourIndex = _12[1];
    var _13 = React.useState(initialPlanningNo || ""), planningNo = _13[0], setPlanningNo = _13[1];
    React.useEffect(function () {
        if (initialPlanningNo) {
            setPlanningNo(initialPlanningNo);
        }
    }, [initialPlanningNo]);
    React.useEffect(function () {
        if (!selectedCostCategory || planningNo)
            return;
        var prefix = "";
        var indexUpdater = null;
        switch (selectedCostCategory) {
            case "Material":
                prefix = "RDM_";
                indexUpdater = setMaterialIndex;
                break;
            case "Department":
                prefix = "RDD_";
                indexUpdater = setDepartmentIndex;
                break;
            case "Outsource":
                prefix = "RDO_";
                indexUpdater = setOutsourceIndex;
                break;
            case "Labour":
                prefix = "RDL_";
                indexUpdater = setLabourIndex;
                break;
        }
        if (indexUpdater) {
            indexUpdater(function (prev) {
                var newIndex = prev + 1;
                setPlanningNo(prefix + newIndex);
                return newIndex;
            });
        }
    }, [selectedCostCategory, planningNo]);
    var renderPopupForm = function () {
        if (!showPopup)
            return null;
        return (React.createElement("div", { style: modalBackdropStyle },
            React.createElement("div", { style: modalStyle },
                React.createElement("h2", { style: { textAlign: 'center', backgroundColor: '#0078d4', color: '#fff', padding: '10px' } }, "PROJECT BUDGET"),
                React.createElement("div", { style: formGridStyle },
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Cost Category *"),
                        React.createElement("select", { value: selectedCostCategory, onChange: function (e) {
                                setSelectedCostCategory(e.target.value);
                                setPlanningNo(""); // Reset planning number when category changes
                            } },
                            React.createElement("option", { value: "" }, "Select Category"),
                            costCategoryOptions
                                .filter(function (cat) { return !cat.toLowerCase().includes('department'); })
                                .map(function (cat, idx) { return (React.createElement("option", { key: idx, value: cat }, cat)); }))),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Cost Type *"),
                        React.createElement("select", { value: selectedCostTypeId, onChange: function (e) { return setSelectedCostTypeId(Number(e.target.value)); } },
                            React.createElement("option", { value: "" }, "Select Type"),
                            costTypeOptions.map(function (type) { return (React.createElement("option", { key: type.Id, value: type.Id }, type.Title)); }))),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Cost Description *"),
                        React.createElement("select", { value: selectedCostDescriptionId, onChange: function (e) { return setSelectedCostDescriptionId(Number(e.target.value)); } },
                            React.createElement("option", { value: "" }, "Select Cost Description"),
                            costDescriptionOptions.map(function (desc) { return (React.createElement("option", { key: desc.Id, value: desc.Id }, desc.Title)); }))),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Project Name *"),
                        React.createElement("select", { value: selectedProjectId, onChange: function (e) {
                                var val = e.target.value;
                                setSelectedProjectId(val === "" ? "" : Number(val));
                            } },
                            React.createElement("option", { value: "" }, "Select Project"),
                            projectOptions.map(function (project) { return (React.createElement("option", { key: project.Id, value: project.Id }, project.Title)); }))),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Sub Project Name *"),
                        React.createElement("select", { value: selectedSubProjectId, onChange: function (e) { return setSelectedSubProjectId(Number(e.target.value)); } },
                            React.createElement("option", { value: "" }, "Select Sub Project"),
                            subProjectOptions.map(function (sub) { return (React.createElement("option", { key: sub.Id, value: sub.Id }, sub.Title)); })),
                        projectLeader && (React.createElement(React.Fragment, null,
                            React.createElement("div", { className: styles.formGroup },
                                React.createElement("label", null, "Project Leader"),
                                React.createElement("input", { type: "text", value: projectLeader, readOnly: true })),
                            React.createElement("div", { className: styles.formGroup },
                                React.createElement("label", null, "Planning No"),
                                React.createElement("input", { type: "text", value: planningNo, readOnly: true }))))),
                    React.createElement("div", null),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Activity Category *"),
                        React.createElement("select", { value: selectedActivityCategoryId, onChange: function (e) { return setSelectedActivityCategoryId(Number(e.target.value)); } }, activityCategories.map(function (cat) { return (React.createElement("option", { key: cat.Id, value: cat.Id }, cat.Title)); }))),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Activity Type *"),
                        React.createElement("select", { value: selectedActivityTypeId, onChange: function (e) { return setSelectedActivityTypeId(Number(e.target.value)); } }, activityTypes.map(function (type) { return (React.createElement("option", { key: type.id, value: type.id }, type.label)); }))),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Feature Description *"),
                        React.createElement("select", { style: { position: 'relative', zIndex: 10 }, value: selectedFeatureDescriptionId, onChange: function (e) { return setSelectedFeatureDescriptionId(Number(e.target.value)); } }, featureDescriptions.map(function (desc) { return (React.createElement("option", { key: desc.id, value: desc.id }, desc.label)); }))),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Activity Description *"),
                        React.createElement("textarea", { rows: 3, style: textareaStyle, value: activityDescription, onChange: function (e) { return setActivityDescription(e.target.value); }, onFocus: function () { return setTextareaFocus(true); }, onBlur: function () { return setTextareaFocus(false); } })),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Free Space/Remark"),
                        React.createElement("textarea", { rows: 3, style: textareaStyle, onFocus: function () { return setTextareaFocus(true); }, onBlur: function () { return setTextareaFocus(false); } })),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "SLA/C4D HDEP"),
                        React.createElement("textarea", { rows: 3, style: textareaStyle, onFocus: function () { return setTextareaFocus(true); }, onBlur: function () { return setTextareaFocus(false); } }))),
                React.createElement("div", { style: { marginTop: '20px' } },
                    React.createElement("div", null, selectedCostCategory === "Material" &&
                        React.createElement(MaterialTable, { index: 0, tblCounter: tableYears.length, curYear: curYear, createMaterialTable: createMaterialTable, deleteSelectedTables: deleteSelectedTables, selectedYears: selectedYears, toggleYearSelection: toggleYearSelection, tableYears: tableYears, rowData: rowData, spendingLevels: spendingLevels, editableLevels: editableLevels, handleMonthChange: handleMonthChange, isModal: false }))),
                React.createElement("div", { style: { marginTop: '20px' } },
                    React.createElement("div", null, selectedCostCategory === "Outsourcing Labor" && (React.createElement(OutsourceLabourTable, { outsourceYears: outsourceYears, selectedOutsourceYears: selectedOutsourceYears, outsourceRowData: outsourceRowData, addOutsourceTable: addOutsourceTable, deleteSelectedOutsourceTables: deleteSelectedOutsourceTables, toggleOutsourceYear: toggleOutsourceYear, handleOutsourceMonthChange: handleOutsourceMonthChange }))),
                    React.createElement("div", null, selectedCostCategory === "Internal Labor" && (React.createElement(InternalLabourTable, { labourData: internalLabourData, onDataChange: handleInternalLabourDataChange, selectedYears: selectedInternalYears, toggleYearSelection: toggleInternalYearSelection, addYear: addInternalYear, deleteYears: deleteSelectedInternalYears, calculateTotalsByQuarter: calculateTotalsByQuarter, department: Department, isModal: false })))),
                React.createElement("div", { style: { marginTop: '20px' } },
                    React.createElement("div", null)),
                React.createElement("div", { style: { display: 'flex', justifyContent: 'flex-end', marginTop: '12px' } },
                    React.createElement("button", { style: okButtonStyle, onClick: handleSave }, "Ok"),
                    React.createElement("button", { style: cancelButtonStyle, onClick: function () { return setShowPopup(false); } }, "Cancel")))));
    };
    var renderDepartmentPopupForm = function () {
        if (!showDepartmentPopup)
            return null;
        return (React.createElement("div", { style: modalBackdropStyle },
            React.createElement("div", { style: modalStyle },
                React.createElement("h2", { style: { textAlign: 'center', backgroundColor: '#0078d4', color: '#fff', padding: '10px' } }, "DEPARTMENT BUDGET"),
                React.createElement("div", { style: formGridStyle },
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Cost Category *"),
                        React.createElement("select", { value: selectedCostCategory, onChange: function (e) {
                                setSelectedCostCategory(e.target.value);
                                setPlanningNo("");
                            } },
                            React.createElement("option", { value: "" }, "Select Category"),
                            costCategoryOptions
                                .filter(function (cat) { return cat.toLowerCase().includes('department'); })
                                .map(function (cat, idx) { return (React.createElement("option", { key: idx, value: cat }, cat)); }))),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Cost Type *"),
                        React.createElement("select", { value: selectedCostTypeId, onChange: function (e) { return setSelectedCostTypeId(Number(e.target.value)); } },
                            React.createElement("option", { value: "" }, "Select Type"),
                            costTypeOptions.map(function (type) { return (React.createElement("option", { key: type.Id, value: type.Id }, type.Title)); }))),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Cost Description *"),
                        React.createElement("select", { value: selectedCostDescriptionId, onChange: function (e) { return setSelectedCostDescriptionId(Number(e.target.value)); } },
                            React.createElement("option", { value: "" }, "Select Cost Description"),
                            costDescriptionOptions.map(function (desc) { return (React.createElement("option", { key: desc.Id, value: desc.Id }, desc.Title)); }))),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Activity Description *"),
                        React.createElement("textarea", { rows: 3, style: textareaStyle, value: activityDescription, onChange: function (e) { return setActivityDescription(e.target.value); }, onFocus: function () { return setTextareaFocus(true); }, onBlur: function () { return setTextareaFocus(false); } })),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "Free Space/Remark"),
                        React.createElement("textarea", { rows: 3, style: textareaStyle, onFocus: function () { return setTextareaFocus(true); }, onBlur: function () { return setTextareaFocus(false); } })),
                    React.createElement("div", { className: styles.formGroup },
                        React.createElement("label", null, "SLA/C4D HDEP"),
                        React.createElement("textarea", { rows: 3, style: textareaStyle, onFocus: function () { return setTextareaFocus(true); }, onBlur: function () { return setTextareaFocus(false); } }))),
                React.createElement("div", { style: { marginTop: '20px' } }, selectedCostCategory === "Department" && (React.createElement(DepartmentTable, { index: 0, tblCounter: tableYears.length, curYear: curYear, createDepartmentTable: createDepartmentTable, deleteSelectedTables: deleteSelectedTables, selectedYears: selectedYears, toggleYearSelection: toggleYearSelection, tableYears: tableYears, rowData: rowData, spendingLevels: spendingLevels, editableLevels: editableLevels, handleMonthChange: handleMonthChange }))),
                React.createElement("div", { style: { display: 'flex', justifyContent: 'flex-end', marginTop: '12px' } },
                    React.createElement("button", { style: okButtonStyle, onClick: handleSaveDepartmentBudget }, "Ok"),
                    React.createElement("button", { style: cancelButtonStyle, onClick: function () { return setShowDepartmentPopup(false); } }, "Cancel")))));
    };
    //ERROR Popup
    var renderErrorPopup = function () {
        if (!errorPopupVisible)
            return null;
        return (React.createElement("div", { style: modalBackdropStyle },
            React.createElement("div", { style: errorModalStyle },
                React.createElement("div", { style: { textAlign: 'center' } },
                    React.createElement("div", { style: { fontSize: '50px', color: '#f44336' } }, "\u274C"),
                    React.createElement("p", { style: { fontSize: '18px', fontWeight: 500 } }, errorMessage),
                    React.createElement("button", { style: okButtonStyle, onClick: function () { return setErrorPopupVisible(false); } }, "OK")))));
    };
    //MATERIAL TABLE FUNCTIOANLITY
    var _14 = React.useState([curYear]), tableYears = _14[0], setTableYears = _14[1];
    var _15 = React.useState([]), selectedYears = _15[0], setSelectedYears = _15[1];
    var createMaterialTable = function () {
        setTableYears(function (prev) {
            var lastYear = prev[prev.length - 1];
            if (lastYear >= 2035)
                return prev; // Optional: limit to 2035
            return __spreadArray(__spreadArray([], prev, true), [lastYear + 1], false);
        });
    };
    var createDepartmentTable = function () {
        setTableYears(function (prev) {
            var lastYear = prev[prev.length - 1];
            if (lastYear >= 2035)
                return prev; // Optional: limit to 2035
            return __spreadArray(__spreadArray([], prev, true), [lastYear + 1], false);
        });
    };
    var toggleYearSelection = function (year) {
        setSelectedYears(function (prev) {
            return prev.includes(year) ? prev.filter(function (y) { return y !== year; }) : __spreadArray(__spreadArray([], prev, true), [year], false);
        });
    };
    var deleteSelectedTables = function () {
        setTableYears(function (prev) { return prev.filter(function (year) { return !selectedYears.includes(year); }); });
        setSelectedYears([]); // Clear selection after deletion
    };
    var spendingLevels = [
        "SL2_Committee approval Plan",
        "SL2_Committee approval Actual",
        "SL3_PR release Plan",
        "SL3_PR release Actual - (cbFC)",
        "SL4 Payment Plan",
        "SL4 Payment Actual -(cbFC)",
    ];
    var editableLevels = new Set([
        "SL2_Committee approval Plan",
        "SL3_PR release Plan",
        "SL4 Payment Plan",
    ]);
    var calculateYearSummary = function (monthlyValues, spendingLevel, year, planValue // optional like "SL4 Payment Plan"
    ) {
        var now = new Date();
        var currentYear = now.getFullYear();
        var curMonth = now.getMonth(); // 0=Jan, 11=Dec
        var total = monthlyValues.reduce(function (sum, val) { return sum + (isNaN(val) ? 0 : val); }, 0);
        var op = 0, ea1 = 0, ea2 = 0, yearEnd = 0;
        if (year === currentYear) {
            if (curMonth === 0) {
                op = total;
            }
            else if (curMonth > 0 && curMonth < 6) {
                ea1 = total;
            }
            else if (curMonth >= 6 && curMonth < 8) {
                ea2 = total;
            }
            else if (curMonth >= 8 && curMonth <= 11) {
                yearEnd = total;
            }
        }
        else {
            op = total;
        }
        return {
            op: op,
            ea1: ea1,
            ea2: ea2,
            yearEnd: yearEnd,
            total: total,
        };
    };
    var _16 = React.useState(tableYears.map(function (year) { return ({
        year: year,
        rows: spendingLevels.map(function (level) { return ({
            spendingLevel: level,
            months: Array(12).fill(""),
            totals: { op: "", ea1: "", ea2: "", yearEnd: "", total: "" },
        }); }),
    }); })), rowData = _16[0], setRowData = _16[1];
    var formatMoney = function (value) {
        if (typeof value !== "number" || isNaN(value))
            return "";
        return value.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    };
    var handleMonthChange = function (yearIdx, rowIdx, monthIdx, value) {
        setRowData(function (prev) {
            var newData = __spreadArray([], prev, true);
            var row = newData[yearIdx].rows[rowIdx];
            var newMonths = __spreadArray([], row.months, true);
            newMonths[monthIdx] = value;
            var monthNumbers = newMonths.map(function (val) { return parseFloat(val) || 0; });
            var _a = calculateYearSummary(monthNumbers, row.spendingLevel, newData[yearIdx].year, row.spendingLevel), op = _a.op, ea1 = _a.ea1, ea2 = _a.ea2, yearEnd = _a.yearEnd, total = _a.total;
            newData[yearIdx].rows[rowIdx] = __assign(__assign({}, row), { months: newMonths, totals: {
                    op: op ? formatMoney(op) : "",
                    ea1: ea1 ? formatMoney(ea1) : "",
                    ea2: ea2 ? formatMoney(ea2) : "",
                    yearEnd: yearEnd ? formatMoney(yearEnd) : "",
                    total: row.spendingLevel === "SL4 Payment Plan" && total ? formatMoney(total) : "",
                } });
            return newData;
        });
    };
    /** ---------- AFTER SAVE FUNCTIONALITY ---------- **/
    var _17 = React.useState([]), yearlyPlanningDate = _17[0], setYearlyPlanningDate = _17[1];
    var _18 = React.useState([]), setYearlyData = _18[1];
    var parseMoney = function (s) {
        if (s === undefined || s === null)
            return 0;
        if (typeof s === "number")
            return isFinite(s) ? s : 0;
        var cleaned = s.replace(/,/g, "").trim();
        var n = parseFloat(cleaned);
        return isFinite(n) ? n : 0;
    };
    var isEmpty = function (v) { return v === undefined || v === null || v === ""; };
    var buildRowsForYear = function (yearIdx) {
        var _a;
        var y = rowData[yearIdx];
        var rowPlanningData = [];
        var rowbreakupData = [];
        // sum "Total Year Plan (T-JPY)" for this year from your “SL4 Payment Plan” totals
        var sl4 = y.rows.find(function (r) { return r.spendingLevel === "SL4 Payment Plan"; });
        var initialJPY = parseMoney(((_a = sl4 === null || sl4 === void 0 ? void 0 : sl4.totals) === null || _a === void 0 ? void 0 : _a.total) || "");
        var totalHrs = 0; // TODO: if you have hours for Labour, set it here
        var labourRate = ""; // TODO: if you have a selected rate for Labour, set it here
        y.rows.forEach(function (r, idx) {
            var _a, _b, _c, _d;
            var months = (r.months || []).map(function (m) { return parseMoney(m); });
            var val = {
                initialJPY: initialJPY,
                totalHrs: totalHrs,
                LabourRate: labourRate,
                Year: y.year,
                Type: r.spendingLevel,
                jan: months[0] || 0,
                feb: months[1] || 0,
                mar: months[2] || 0,
                apr: months[3] || 0,
                may: months[4] || 0,
                jun: months[5] || 0,
                jul: months[6] || 0,
                aug: months[7] || 0,
                sep: months[8] || 0,
                oct: months[9] || 0,
                nov: months[10] || 0,
                dec: months[11] || 0,
                op: parseMoney((_a = r.totals) === null || _a === void 0 ? void 0 : _a.op) || 0,
                ea1: parseMoney((_b = r.totals) === null || _b === void 0 ? void 0 : _b.ea1) || 0,
                ea2: parseMoney((_c = r.totals) === null || _c === void 0 ? void 0 : _c.ea2) || 0,
                yearEnd: parseMoney((_d = r.totals) === null || _d === void 0 ? void 0 : _d.yearEnd) || 0,
                comments: ""
            };
            // “rowPlanningData” used to be {key, value}; keep a stable key like in your old code
            var key = "".concat(planningNo, "_").concat(selectedCostCategory, "_").concat(y.year, "_").concat(idx + 1);
            rowPlanningData.push({ key: key, value: val });
            rowbreakupData.push(val);
        });
        return { rowPlanningData: rowPlanningData, rowbreakupData: rowbreakupData, initialJPY: initialJPY };
    };
    var buildAllMonthlyArrays = function () {
        var monthlyPlanningData = [];
        var monthlyBreakData = [];
        rowData.forEach(function (yBlock, yearIdx) {
            var _a = buildRowsForYear(yearIdx), rowPlanningData = _a.rowPlanningData, rowbreakupData = _a.rowbreakupData;
            var tableKey = "".concat(planningNo, "_").concat(selectedCostCategory, "_").concat(yearIdx);
            monthlyPlanningData.push({ key: tableKey, value: rowPlanningData });
            var tblYear = yBlock.year; // Angular used the selected ddlYear as key
            monthlyBreakData.push({ key: tblYear, value: rowbreakupData });
        });
        return { monthlyPlanningData: monthlyPlanningData, monthlyBreakData: monthlyBreakData };
    };
    var loadRowsFromMonthlyJSONData = function (data) {
        return data.map(function (block) { return ({
            year: Number(block.key),
            rows: Array.isArray(block === null || block === void 0 ? void 0 : block.value)
                ? block.value.map(function (entry) {
                    var _a, _b, _c, _d, _e, _f;
                    return ({
                        spendingLevel: entry.Type,
                        months: ((_a = entry.monthly) === null || _a === void 0 ? void 0 : _a.length) === 12 ? entry.monthly : Array(12).fill(""),
                        totals: {
                            total: (_b = entry.initialJPY) !== null && _b !== void 0 ? _b : "",
                            op: (_c = entry.op) !== null && _c !== void 0 ? _c : "",
                            ea1: (_d = entry.ea1) !== null && _d !== void 0 ? _d : "",
                            ea2: (_e = entry.ea2) !== null && _e !== void 0 ? _e : "",
                            yearEnd: (_f = entry.yearEnd) !== null && _f !== void 0 ? _f : "",
                        },
                    });
                })
                : [], // fallback if block.value is not an array
        }); });
    };
    var _19 = React.useState(false), showYearlyDetailModal = _19[0], setShowYearlyDetailModal = _19[1];
    var validateMaterialData = function (rowData) {
        var _a;
        if (isEmpty(selectedCostCategory))
            return "Please select Cost Category.";
        if (isEmpty(selectedCostTypeId))
            return "Please select Cost Type.";
        if (isEmpty(selectedCostDescriptionId))
            return "Please select Cost Description.";
        if (isEmpty(selectedProjectId))
            return "Please select Project.";
        if (isEmpty(selectedSubProjectId))
            return "Please select Sub Project.";
        if (isEmpty(selectedActivityCategoryId))
            return "Please select Activity Category.";
        if (isEmpty(selectedActivityTypeId))
            return "Please select Activity Type.";
        if (isEmpty(selectedFeatureDescriptionId))
            return "Please select Feature Description.";
        if (isEmpty(activityDescription === null || activityDescription === void 0 ? void 0 : activityDescription.trim()))
            return "Please enter Activity Description.";
        if (!(rowData === null || rowData === void 0 ? void 0 : rowData.length))
            return "Please add at least one year table.";
        for (var _i = 0, rowData_1 = rowData; _i < rowData_1.length; _i++) {
            var y = rowData_1[_i];
            var sl4 = y.rows.find(function (r) { return r.spendingLevel === "SL4 Payment Plan"; });
            var total = parseMoney(((_a = sl4 === null || sl4 === void 0 ? void 0 : sl4.totals) === null || _a === void 0 ? void 0 : _a.total) || "");
            if (total <= 0) {
                return "Enter monthly values so that 'SL4 Payment Plan' has a total for year ".concat(y.year, ".");
            }
        }
        return null;
    };
    // SAVE HANDLERS
    var handleSaveMaterialBudget = function () {
        var err = validateMaterialData(rowData);
        if (err) {
            setErrorMessage(err);
            setErrorPopupVisible(true);
            return;
        }
        var _a = buildAllMonthlyArrays(), monthlyPlanningData = _a.monthlyPlanningData, monthlyBreakData = _a.monthlyBreakData;
        var payload = {
            Key: planningNo,
            value: {
                RequestId: 0,
                PlanningNumber: planningNo,
                ActivityCategory: selectedActivityCategoryId || 0,
                ActivityType: selectedActivityTypeId || 0,
                FeatureDescription: selectedFeatureDescriptionId || 0,
                ActivityContent: activityDescription,
                Remarks: "", // TODO: wire to state
                SLA_C4D_x0020_HDEP: "", // TODO: wire to state
                CostCategory: selectedCostCategory,
                CostType: selectedCostTypeId,
                CostDescription: selectedCostDescriptionId,
                projCategory: 0,
                projCluster: 0,
                projName: selectedProjectId || 0,
                subprojName: selectedSubProjectId || 0,
                projectLeaderID: projectLeader || "",
                planApproval: "0", // Always "0" for project-based
                MonthlyBreakup: monthlyPlanningData,
                MonthlyJSONData: monthlyBreakData
            }
        };
        setYearlyPlanningDate(function (prev) {
            var next = __spreadArray(__spreadArray([], prev, true), [payload], false);
            setYearlyData(next);
            return next;
        });
        setShowPopup(false);
    };
    //Department after save
    //VALIDATION FOR SAVE
    var validateDepartmentData = function (rowData) {
        var _a;
        if (isEmpty(selectedCostCategory))
            return "Please select Cost Category.";
        if (isEmpty(selectedCostTypeId))
            return "Please select Cost Type.";
        if (isEmpty(selectedCostDescriptionId))
            return "Please select Cost Description.";
        if (isEmpty(selectedActivityTypeId))
            return "Please select Activity Type.";
        if (isEmpty(selectedFeatureDescriptionId))
            return "Please select Feature Description.";
        if (isEmpty(activityDescription === null || activityDescription === void 0 ? void 0 : activityDescription.trim()))
            return "Please enter Activity Description.";
        if (!(rowData === null || rowData === void 0 ? void 0 : rowData.length))
            return "Please add at least one year table.";
        for (var _i = 0, rowData_2 = rowData; _i < rowData_2.length; _i++) {
            var y = rowData_2[_i];
            var sl4 = y.rows.find(function (r) { return r.spendingLevel === "SL4 Payment Plan"; });
            var total = parseMoney(((_a = sl4 === null || sl4 === void 0 ? void 0 : sl4.totals) === null || _a === void 0 ? void 0 : _a.total) || "");
            if (total <= 0) {
                return "Enter monthly values so that 'SL4 Payment Plan' has a total for year ".concat(y.year, ".");
            }
        }
        return null;
    };
    var handleSaveDepartmentBudget = function () {
        var err = validateDepartmentData(rowData);
        if (err) {
            setErrorMessage(err);
            setErrorPopupVisible(true);
            return;
        }
        var _a = buildAllMonthlyArrays(), monthlyPlanningData = _a.monthlyPlanningData, monthlyBreakData = _a.monthlyBreakData;
        console.log(monthlyBreakData, monthlyPlanningData);
        var payload = {
            Key: planningNo,
            value: {
                RequestId: 0,
                PlanningNumber: planningNo,
                ActivityCategory: selectedActivityCategoryId || 0,
                ActivityType: selectedActivityTypeId || 0,
                FeatureDescription: selectedFeatureDescriptionId || 0,
                ActivityContent: activityDescription,
                Remarks: "", // TODO: wire to state
                SLA_C4D_x0020_HDEP: "", // TODO: wire to state
                CostCategory: selectedCostCategory,
                CostType: selectedCostTypeId,
                CostDescription: selectedCostDescriptionId,
                projCategory: 0,
                projCluster: 0,
                projName: selectedProjectId || 0,
                subprojName: selectedSubProjectId || 0,
                projectLeaderID: projectLeader || "",
                planApproval: "0", // Always "0" for project-based
                MonthlyBreakup: monthlyPlanningData,
                MonthlyJSONData: monthlyBreakData
            }
        };
        setYearlyPlanningDate(function (prev) {
            var next = __spreadArray(__spreadArray([], prev, true), [payload], false);
            setYearlyData(next);
            return next;
        });
        setShowDepartmentPopup(false);
    };
    var renderDepartmentYearlyPlanSummary = function () {
        return (React.createElement("table", { style: { borderCollapse: "collapse", width: "100%", marginTop: "20px" } },
            React.createElement("thead", null,
                React.createElement("tr", { style: { backgroundColor: "#f0f0f0", textAlign: "center" } },
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, "Target year"),
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, "Total Year Plan (T-JPY)"),
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, "Monthly break-down / Change History"))),
            React.createElement("tbody", null, yearlyPlanningDate.map(function (item, idx) {
                var _a;
                var monthlyBreaks = ((_a = item === null || item === void 0 ? void 0 : item.value) === null || _a === void 0 ? void 0 : _a.MonthlyJSONData) || [];
                return monthlyBreaks.map(function (yearBlock, innerIdx) {
                    var entries = Array.isArray(yearBlock === null || yearBlock === void 0 ? void 0 : yearBlock.value) ? yearBlock.value : [];
                    var matchType = "SL4 Payment Plan"; // default for Material
                    if (selectedCostCategory === "Outsourcing Labor") {
                        matchType = "FTE Budget Plan (T-JPY)";
                    }
                    var totalForType = entries
                        .filter(function (entry) { return entry.Type === matchType; })
                        .reduce(function (sum, entry) { return sum + parseMoney(entry.initialJPY); }, 0);
                    return (React.createElement("tr", { key: "".concat(idx, "-").concat(innerIdx), style: { textAlign: "center" } },
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } }, yearBlock.key),
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } }, totalForType.toFixed(2)),
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } },
                            React.createElement("button", { onClick: function () { return handleDepartmentLinkClick(item, yearBlock); }, style: { padding: "5px 10px" } }, "Link"))));
                });
            }))));
    };
    var handleDepartmentLinkClick = function (item, yearBlock) {
        if (yearBlock) {
            // loadRowsFromMonthlyJSONData expects an array of such objects,
            // so wrap yearBlock in an array
            loadRowsFromMonthlyJSONData([yearBlock]);
        }
        setSelectedYearBlock(yearBlock);
        setShowYearlyDetailModal(true);
    };
    //END OF DEPARTMENT
    //START LABOUR VALIDATION
    var validateLabourData = function (outsourceRowData, outsourceYears) {
        if (!(outsourceRowData === null || outsourceRowData === void 0 ? void 0 : outsourceRowData.length))
            return "Please add at least one outsource labour year table.";
        if (isEmpty(selectedCostCategory))
            return "Please select Cost Category.";
        if (isEmpty(selectedCostTypeId))
            return "Please select Cost Type.";
        if (isEmpty(selectedCostDescriptionId))
            return "Please select Cost Description.";
        if (isEmpty(selectedProjectId))
            return "Please select Project.";
        if (isEmpty(selectedSubProjectId))
            return "Please select Sub Project.";
        if (isEmpty(selectedActivityCategoryId))
            return "Please select Activity Category.";
        if (isEmpty(selectedActivityTypeId))
            return "Please select Activity Type.";
        if (isEmpty(selectedFeatureDescriptionId))
            return "Please select Feature Description.";
        if (isEmpty(activityDescription === null || activityDescription === void 0 ? void 0 : activityDescription.trim()))
            return "Please enter Activity Description.";
        if (!(outsourceRowData === null || outsourceRowData === void 0 ? void 0 : outsourceRowData.length))
            return "Please add at least one outsource labour year table.";
        for (var idx = 0; idx < outsourceRowData.length; idx++) {
            var yearData = outsourceRowData[idx];
            yearData.forEach(function (row, i) {
            });
            var year = outsourceYears[idx] || "unknown";
            // Find the 'FTE Budget Plan (T-JPY)' row with trimmed category name
            var internalLaborPlanRow = yearData.find(function (row) {
                var _a;
                return ((_a = row.category) === null || _a === void 0 ? void 0 : _a.trim()) === "FTE Budget Plan (T-JPY)";
            });
            if (!internalLaborPlanRow) {
                return "Missing 'FTE Budget Plan (T-JPY)' row in labor data for year ".concat(year, ".");
            }
            // Check if the months array exists and has exactly 12 months, but allow some missing values
            if (!Array.isArray(internalLaborPlanRow.months) || internalLaborPlanRow.months.length !== 12) {
                return "Months array must have 12 entries for 'FTE Budget Plan (T-JPY)' for year ".concat(year, ".");
            }
            // Check that each month is either a valid value or empty
            var invalidMonths = internalLaborPlanRow.months.filter(function (month) { return month !== "" && isNaN(parseFloat(month)); });
            if (invalidMonths.length > 0) {
                return "Invalid month value found in 'FTE Budget Plan (T-JPY)' for year ".concat(year, ". Only numbers or empty values are allowed.");
            }
        }
        return null;
    };
    var handleSaveLabourBudget = function () {
        try {
            var err = validateLabourData(outsourceRowData, outsourceYears);
            if (err) {
                alert(err);
                setErrorMessage(err);
                setErrorPopupVisible(true);
                return;
            }
            var _a = buildOutsourceMonthlyArrays(outsourceRowData, outsourceYears), monthlyPlanningData = _a.monthlyPlanningData, monthlyBreakData = _a.monthlyBreakData;
            var payload = {
                Key: planningNo,
                value: {
                    RequestId: 0,
                    PlanningNumber: planningNo,
                    ActivityCategory: selectedActivityCategoryId || 0,
                    ActivityType: selectedActivityTypeId || 0,
                    FeatureDescription: selectedFeatureDescriptionId || 0,
                    ActivityContent: activityDescription,
                    Remarks: "",
                    SLA_C4D_x0020_HDEP: "",
                    CostCategory: selectedCostCategory,
                    CostType: selectedCostTypeId,
                    CostDescription: selectedCostDescriptionId,
                    projCategory: 0,
                    projCluster: 0,
                    projName: selectedProjectId || 0,
                    subprojName: selectedSubProjectId || 0,
                    projectLeaderID: projectLeader || "",
                    planApproval: "0",
                    MonthlyBreakup: monthlyPlanningData,
                    MonthlyJSONData: monthlyBreakData,
                }
            };
            if (!payload.value ||
                !Array.isArray(payload.value.MonthlyBreakup) ||
                !Array.isArray(payload.value.MonthlyJSONData)) {
                console.error("Invalid payload:", payload);
                alert("Invalid data format. Check console.");
                return;
            }
            var next = __spreadArray(__spreadArray([], (yearlyPlanningDate || []), true), [payload], false);
            setYearlyPlanningDate(next);
            setYearlyData(next);
            setShowPopup(false);
        }
        catch (ex) {
            console.error("Error in handleSaveLabourBudget:", ex);
            alert("Unexpected error occurred. Check console for details.");
        }
    };
    //OUTSOURCE TABLE AFTER SAVE
    var buildOutsourceRowsForYear = function (yearIdx, outsourceRowData, outsourceYears) {
        var yearTable = outsourceRowData[yearIdx];
        if (!yearTable) {
            console.warn("No data found for year index ".concat(yearIdx));
            return { rowPlanningData: [], rowbreakupData: [], initialJPY: 0 };
        }
        var rowPlanningData = [];
        var rowbreakupData = [];
        yearTable.forEach(function (row, idx) {
            var _a, _b;
            var months = (row.months || []).map(function (m) { return parseMoney(m); });
            var val = {
                Type: row.category,
                Year: outsourceYears[yearIdx],
                initialJPY: parseMoney(((_a = row.yearSummary) === null || _a === void 0 ? void 0 : _a.plan) || 0),
                fte: ((_b = row.yearSummary) === null || _b === void 0 ? void 0 : _b.fte) || 0,
                jan: months[0] || 0,
                feb: months[1] || 0,
                mar: months[2] || 0,
                apr: months[3] || 0,
                may: months[4] || 0,
                jun: months[5] || 0,
                jul: months[6] || 0,
                aug: months[7] || 0,
                sep: months[8] || 0,
                oct: months[9] || 0,
                nov: months[10] || 0,
                dec: months[11] || 0,
                op: parseMoney(row.totals.op),
                ea1: parseMoney(row.totals.ea1),
                ea2: parseMoney(row.totals.ea2),
                yearEnd: parseMoney(row.totals.yearEnd),
                comments: ""
            };
            var year = outsourceYears[yearIdx];
            var key = "".concat(planningNo, "_").concat(selectedCostCategory, "_").concat(year, "_").concat(idx + 1);
            rowPlanningData.push({ key: key, value: val });
            rowbreakupData.push(val);
        });
        return {
            rowPlanningData: rowPlanningData,
            rowbreakupData: rowbreakupData,
            initialJPY: rowPlanningData.reduce(function (sum, r) { return sum + parseMoney(r.value.initialJPY); }, 0)
        };
    };
    var buildOutsourceMonthlyArrays = function (outsourceRowData, outsourceYears) {
        var allPlanningData = [];
        var allBreakupData = [];
        outsourceYears.forEach(function (year, idx) {
            var _a = buildOutsourceRowsForYear(idx, outsourceRowData, outsourceYears), rowPlanningData = _a.rowPlanningData, rowbreakupData = _a.rowbreakupData;
            allPlanningData.push.apply(allPlanningData, rowPlanningData);
            // 👇 Push grouped data by year
            allBreakupData.push({
                key: year.toString(),
                value: rowbreakupData
            });
        });
        return {
            monthlyPlanningData: allPlanningData,
            monthlyBreakData: allBreakupData
        };
    };
    //On LINK CLICK AFTER SAVE
    var _20 = React.useState(null), selectedYearBlock = _20[0], setSelectedYearBlock = _20[1];
    var handleLinkClick = function (item, yearBlock) {
        if (yearBlock) {
            // loadRowsFromMonthlyJSONData expects an array of such objects,
            // so wrap yearBlock in an array
            loadRowsFromMonthlyJSONData([yearBlock]);
        }
        setSelectedYearBlock(yearBlock);
        setShowYearlyDetailModal(true);
    };
    var handleModalClose = function () {
        setShowYearlyDetailModal(false);
        setSelectedYearBlock(null);
    };
    var renderYearlyPlanSummary = function () {
        return (React.createElement("table", { style: { borderCollapse: "collapse", width: "100%", marginTop: "20px" } },
            React.createElement("thead", null,
                React.createElement("tr", { style: { backgroundColor: "#f0f0f0", textAlign: "center" } },
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, "Target year"),
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, "Total Year Plan (T-JPY)"),
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, "Monthly break-down / Change History"))),
            React.createElement("tbody", null, yearlyPlanningDate.map(function (item, idx) {
                var _a;
                var monthlyBreaks = ((_a = item === null || item === void 0 ? void 0 : item.value) === null || _a === void 0 ? void 0 : _a.MonthlyJSONData) || [];
                return monthlyBreaks.map(function (yearBlock, innerIdx) {
                    var entries = Array.isArray(yearBlock === null || yearBlock === void 0 ? void 0 : yearBlock.value) ? yearBlock.value : [];
                    var matchType = "SL4 Payment Plan"; // default for Material
                    if (selectedCostCategory === "Outsourcing Labor") {
                        matchType = "FTE Budget Plan (T-JPY)";
                    }
                    var totalForType = entries
                        .filter(function (entry) { return entry.Type === matchType; })
                        .reduce(function (sum, entry) { return sum + parseMoney(entry.initialJPY); }, 0);
                    return (React.createElement("tr", { key: "".concat(idx, "-").concat(innerIdx), style: { textAlign: "center" } },
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } }, yearBlock.key),
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } }, totalForType.toFixed(2)),
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } },
                            React.createElement("button", { onClick: function () { return handleLinkClick(item, yearBlock); }, style: { padding: "5px 10px" } }, "Link"))));
                });
            }))));
    };
    //Dedlete Functionality
    var _21 = React.useState(new Set()), selectedRows = _21[0], setSelectedRows = _21[1]; // track selected rows by index
    // Toggle checkbox selection
    var toggleRowSelection = function (index) {
        setSelectedRows(function (prev) {
            var newSet = new Set(prev);
            if (newSet.has(index)) {
                newSet.delete(index);
            }
            else {
                newSet.add(index);
            }
            return newSet;
        });
    };
    // Delete selected rows
    var deleteSelectedRows = function () {
        setYearlyPlanningDate(function (prev) {
            return prev.filter(function (_, idx) { return !selectedRows.has(idx); });
        });
        setSelectedRows(new Set()); // reset selection
    };
    var _22 = React.useState([]), viewModeOutsourceYears = _22[0], setViewModeOutsourceYears = _22[1];
    var _23 = React.useState([]), viewModeOutsourceRowData = _23[0], setViewModeOutsourceRowData = _23[1];
    var _24 = React.useState(false), showViewTable = _24[0], setShowViewTable = _24[1];
    var renderOutsourceLabourSummary = function () {
        return (React.createElement("table", { style: { borderCollapse: "collapse", width: "100%", marginTop: "20px" } },
            React.createElement("thead", null,
                React.createElement("tr", { style: { backgroundColor: "#f0f0f0", textAlign: "center" } },
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, "Target year"),
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, "Total Year Plan (T-JPY)"),
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, "Monthly break-down / Change History"))),
            React.createElement("tbody", null, yearlyPlanningDate.map(function (item, idx) {
                var _a;
                var monthlyBreaks = ((_a = item === null || item === void 0 ? void 0 : item.value) === null || _a === void 0 ? void 0 : _a.MonthlyJSONData) || [];
                return monthlyBreaks.map(function (yearBlock, innerIdx) {
                    var entries = Array.isArray(yearBlock === null || yearBlock === void 0 ? void 0 : yearBlock.value) ? yearBlock.value : [];
                    // Get total JPY sum from all rows
                    var matchType = "FTE Budget Plan (T-JPY"; // default for Material
                    var totalJPY = entries
                        .filter(function (entry) { return entry.Type === matchType; })
                        .reduce(function (sum, entry) { return sum + parseMoney(entry.initialJPY); }, 0);
                    return (React.createElement("tr", { key: "".concat(idx, "-").concat(innerIdx), style: { textAlign: "center" } },
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } }, yearBlock.key),
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } }, totalJPY.toFixed(2)),
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } },
                            React.createElement("button", { onClick: function () { return handleLabourLinkClick(item, yearBlock); }, style: { padding: "5px 10px" } }, "Link"))));
                });
            }))));
    };
    var handleLabourLinkClick = function (item, yearBlock) {
        var year = parseInt(yearBlock.key);
        var entries = Array.isArray(yearBlock.value) ? yearBlock.value : [];
        var rowData = convertYearBlockToRowData(entries);
        setViewModeOutsourceYears([year]); // Only showing one year here
        setViewModeOutsourceRowData([rowData]);
        setShowViewTable(true); // trigger to show the table
    };
    function convertYearBlockToRowData(entries) {
        var categories = ["FTE Plan (Numbers)", "FTE Budget Plan (T-JPY)", "FTE Budget Actual (T-JPY)"];
        return categories.map(function (cat) {
            var _a, _b, _c, _d;
            var source = entries.find(function (e) { return e.Type === cat; }) || {};
            var months = [
                source.jan, source.feb, source.mar, source.apr,
                source.may, source.jun, source.jul, source.aug,
                source.sep, source.oct, source.nov, source.dec,
            ].map(function (val) { return (val !== undefined ? val.toString() : ""); });
            return {
                category: cat,
                months: months,
                totals: {
                    op: ((_a = source.op) === null || _a === void 0 ? void 0 : _a.toString()) || "",
                    ea1: ((_b = source.ea1) === null || _b === void 0 ? void 0 : _b.toString()) || "",
                    ea2: ((_c = source.ea2) === null || _c === void 0 ? void 0 : _c.toString()) || "",
                    yearEnd: ((_d = source.yearEnd) === null || _d === void 0 ? void 0 : _d.toString()) || "",
                },
                yearSummary: {
                    plan: parseFloat(source.initialJPY || "0"),
                    fte: parseFloat(source.fte || "0"),
                },
            };
        });
    }
    var handleSave = function () {
        if (selectedCostCategory === "Material") {
            handleSaveMaterialBudget();
        }
        else if (selectedCostCategory === "Outsourcing Labor") {
            handleSaveLabourBudget();
        }
        else if (selectedCostCategory === "Internal Labor") {
            handleSaveInternalLaborBudget(); // ✅ Now implemented 
        }
        else {
            setErrorMessage("Unsupported cost category selected.");
            setErrorPopupVisible(true);
        }
    };
    // For internal labour table
    var _25 = React.useState([]), internalLabourData = _25[0], setInternalLabourData = _25[1];
    var _26 = React.useState([]), selectedInternalYears = _26[0], setSelectedInternalYears = _26[1];
    var _27 = React.useState(false), showIntYearlyDetailModal = _27[0], setShowIntYearlyDetailModal = _27[1];
    var _28 = React.useState(null), setSelectedIntYearBlock = _28[1];
    var toggleInternalYearSelection = function (year) {
        setSelectedInternalYears(function (prev) {
            return prev.includes(year) ? prev.filter(function (y) { return y !== year; }) : __spreadArray(__spreadArray([], prev, true), [year], false);
        });
    };
    var handleInternalLabourDataChange = function (updatedData) {
        setInternalLabourData(updatedData);
    };
    var deleteSelectedInternalYears = function () {
        setInternalLabourData(internalLabourData.filter(function (data) { return !selectedInternalYears.includes(data.year); }));
        setSelectedInternalYears([]);
    };
    var calculateTotalsByQuarter = React.useCallback(function (months, rate) {
        var monthVals = months.map(function (m) { return parseFloat(m) || 0; }).map(function (v) { return v * rate; });
        var curMonth = new Date().getMonth();
        var totalVal = monthVals.reduce(function (a, b) { return a + b; }, 0);
        var op = "", ea1 = "", ea2 = "", yearEnd = "";
        if (curMonth <= 0)
            op = totalVal.toFixed(2);
        else if (curMonth < 6)
            ea1 = totalVal.toFixed(2);
        else if (curMonth < 8)
            ea2 = totalVal.toFixed(2);
        else
            yearEnd = totalVal.toFixed(2);
        var result = {
            op: op,
            ea1: ea1,
            ea2: ea2,
            yearEnd: yearEnd,
            total: totalVal.toFixed(2)
        };
        console.log("calculateTotalsByQuarter result:", result);
        return result;
    }, []);
    var _29 = React.useState(false), setInternalLabourSaved = _29[1];
    React.useEffect(function () {
        if (selectedCostCategory === "Internal Labor") {
            var currentYear_1 = new Date().getFullYear();
            // If no internal labour data yet, initialize with current year
            if (internalLabourData.length === 0) {
                var initialYearData = {
                    year: currentYear_1,
                    rows: internalLabourCategories.map(function (category) { return ({
                        category: category,
                        months: new Array(12).fill(""),
                        totals: { op: "", ea1: "", ea2: "", yearEnd: "", total: "" },
                        year: currentYear_1 // ✅ Add this to match your LabourRow type
                    }); })
                };
                setInternalLabourData([initialYearData]);
                setSelectedInternalYears([currentYear_1]); // Select current year by default
            }
        }
        else {
            // Optional: clear internal labour data when switching to another category
            setInternalLabourData([]);
            setSelectedInternalYears([]);
        }
    }, [selectedCostCategory]); // ✅ Complete the dependency array
    var addInternalYear = function () {
        if (internalLabourData.length === 0)
            return;
        var years = internalLabourData.map(function (d) { return d.year; });
        var maxYear = Math.max.apply(Math, years);
        var nextYear = maxYear + 1;
        if (nextYear > 2035)
            return;
        var newYearData = {
            year: nextYear,
            rows: internalLabourCategories.map(function (category) { return ({
                category: category,
                months: new Array(12).fill(""),
                totals: { op: "", ea1: "", ea2: "", yearEnd: "", total: "" },
                year: nextYear // ✅ Add this
            }); })
        };
        setInternalLabourData(__spreadArray(__spreadArray([], internalLabourData, true), [newYearData], false));
    };
    var handleSaveInternalLaborBudget = function () {
        var err = validateInternalLabourData(internalLabourData);
        if (err) {
            setErrorMessage(err);
            setErrorPopupVisible(true);
            return;
        }
        var _a = buildAllMonthlyArraysForLabor(), monthlyPlanningData = _a.monthlyPlanningData, monthlyBreakData = _a.monthlyBreakData;
        console.log("Department Test " + monthlyBreakData, monthlyPlanningData);
        var payload = {
            Key: planningNo,
            value: {
                RequestId: 0,
                PlanningNumber: planningNo,
                MonthlyBreakup: monthlyPlanningData, // ✅ Needed for storage if required elsewhere
                MonthlyJSONData: monthlyBreakData, // ✅ Used by summary table
                LabourDetails: internalLabourData,
                ActivityCategory: selectedActivityCategoryId || 0,
                ActivityType: selectedActivityTypeId || 0,
                FeatureDescription: selectedFeatureDescriptionId || 0,
                ActivityContent: activityDescription,
                Remarks: "", // Wire this to your remark state if you have one
                SLA_C4D_x0020_HDEP: "", // Wire this to your SLA state
                CostCategory: selectedCostCategory,
                CostType: selectedCostTypeId,
                CostDescription: selectedCostDescriptionId,
                projCategory: 0,
                projCluster: 0,
                projName: selectedProjectId || 0,
                subprojName: selectedSubProjectId || 0,
                projectLeaderID: projectLeader || "",
                planApproval: "0",
                //MonthlyBreakup: monthlyPlanningData,
                //MonthlyJSONData: monthlyBreakData,
            }
        };
        setYearlyPlanningDate(function (prev) {
            var next = __spreadArray(__spreadArray([], prev, true), [payload], false);
            setYearlyData(next);
            return next;
        });
        setShowPopup(false);
        setInternalLabourSaved(true);
    };
    var validateInternalLabourData = function (labourData) {
        var _a;
        if (isEmpty(selectedCostCategory))
            return "Please select Cost Category.";
        if (isEmpty(selectedCostTypeId))
            return "Please select Cost Type.";
        if (isEmpty(selectedCostDescriptionId))
            return "Please select Cost Description.";
        if (isEmpty(selectedProjectId))
            return "Please select Project.";
        if (isEmpty(selectedSubProjectId))
            return "Please select Sub Project.";
        if (isEmpty(selectedActivityCategoryId))
            return "Please select Activity Category.";
        if (isEmpty(selectedActivityTypeId))
            return "Please select Activity Type.";
        if (isEmpty(selectedFeatureDescriptionId))
            return "Please select Feature Description.";
        if (isEmpty(activityDescription === null || activityDescription === void 0 ? void 0 : activityDescription.trim()))
            return "Please enter Activity Description.";
        if (!(labourData === null || labourData === void 0 ? void 0 : labourData.length))
            return "Please add at least one year table.";
        if (!(rowData === null || rowData === void 0 ? void 0 : rowData.length))
            return "Please add at least one year table.";
        for (var _i = 0, internalLabourData_1 = internalLabourData; _i < internalLabourData_1.length; _i++) {
            var y = internalLabourData_1[_i];
            // Find the row with spendingLevel "Internal Labor Plan (hrs)"
            var internalLabor = y.rows.find(function (r) { return r.category === "Labor Plan (hrs)"; });
            // Assuming totals.total is a string representing a number, parse it
            var total = parseMoney(((_a = internalLabor === null || internalLabor === void 0 ? void 0 : internalLabor.totals) === null || _a === void 0 ? void 0 : _a.total) || "");
            if (total <= 0) {
                return "Enter monthly values so that 'Labor Plan (hrs)' has a total for year ".concat(y.year, ".");
            }
        }
        return null;
    };
    function buildAllMonthlyArraysForLabor() {
        var monthlyPlanningData = [];
        var monthlyBreakData = [];
        internalLabourData.forEach(function (yearData) {
            var year = yearData.year, rows = yearData.rows;
            var monthlyEntriesForYear = [];
            rows.forEach(function (row) {
                var type = row.category;
                row.months.forEach(function (val, idx) {
                    var month = getMonthName(idx); // e.g., 0 -> "Jan"
                    if (val && val.trim() !== "") {
                        monthlyEntriesForYear.push({
                            Month: month,
                            Type: type,
                            initialJPY: parseFloat(val),
                        });
                    }
                });
            });
            // Push to the final collections
            monthlyPlanningData.push.apply(monthlyPlanningData, monthlyEntriesForYear);
            monthlyBreakData.push({
                key: year.toString(),
                value: monthlyEntriesForYear,
            });
        });
        return { monthlyPlanningData: monthlyPlanningData, monthlyBreakData: monthlyBreakData };
    }
    function getMonthName(index) {
        var months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        return months[index] || "";
    }
    var renderinternalPlanSummary = function () {
        return (React.createElement("table", { style: { borderCollapse: "collapse", width: "100%", marginTop: "20px" } },
            React.createElement("thead", null,
                React.createElement("tr", { style: { backgroundColor: "#f0f0f0", textAlign: "center" } },
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, "Target year"),
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, selectedCostCategory === "Internal Labor" ? "Total Year Plan (hrs)" : "Total Year Plan (T-JPY)"),
                    React.createElement("th", { style: { padding: "10px", border: "1px solid #ccc" } }, "Monthly break-down"))),
            React.createElement("tbody", null, yearlyPlanningDate.map(function (item, idx) {
                var _a;
                var monthlyBreaks = ((_a = item === null || item === void 0 ? void 0 : item.value) === null || _a === void 0 ? void 0 : _a.MonthlyJSONData) || [];
                return monthlyBreaks.map(function (yearBlock, innerIdx) {
                    var entries = Array.isArray(yearBlock === null || yearBlock === void 0 ? void 0 : yearBlock.value) ? yearBlock.value : [];
                    var matchType = "Labor Plan (hrs)";
                    if (selectedCostCategory === "Internal Labor") {
                        matchType = "Labor Plan (hrs)";
                    }
                    var totalForType = entries
                        .filter(function (entry) { return entry.Type === matchType; })
                        .reduce(function (sum, entry) { return sum + (Number(entry.initialJPY) || 0); }, 0);
                    return (React.createElement("tr", { key: "".concat(idx, "-").concat(innerIdx), style: { textAlign: "center" } },
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } }, yearBlock.key),
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } }, !isNaN(totalForType) ? totalForType.toFixed(2) : "0.00"),
                        React.createElement("td", { style: { padding: "10px", border: "1px solid #ccc" } },
                            React.createElement("button", { onClick: function () { return handleinternalLinkClick(item, yearBlock); }, style: { padding: "5px 10px" } }, "Link"))));
                });
            }))));
    };
    var handleinternalLinkClick = function (item, yearBlock) {
        if (!yearBlock)
            return;
        var yearKey = parseInt(yearBlock.key, 10);
        var currentYearData = internalLabourData.find(function (d) { return d.year === yearKey; });
        if (currentYearData) {
            setSelectedIntYearBlock(currentYearData);
            setShowIntYearlyDetailModal(true);
        }
        else {
            setSelectedIntYearBlock(yearBlock);
            setShowIntYearlyDetailModal(true);
        }
    };
    return (React.createElement(React.Fragment, null,
        React.createElement(AllPlanningWp, null),
        React.createElement("div", { className: styles.budgetForm },
            React.createElement("div", { className: styles.buttonGroup },
                React.createElement("button", { className: activeButton === 'project' ? styles.active : styles.inactive, onClick: function () { return setActiveButton('project'); } },
                    React.createElement(Icon, { iconName: "ProjectCollection", style: { marginRight: 8 } }),
                    "Project Budget"),
                React.createElement("button", { className: activeButton === 'department' ? styles.active : styles.inactive, onClick: function () { return setActiveButton('department'); } },
                    React.createElement(Icon, { iconName: "Group", style: { marginRight: 8 } }),
                    "Department Budget")),
            React.createElement("div", { className: styles.formRow },
                React.createElement("div", { className: styles.formGroup },
                    React.createElement("label", null, "Requestor Name"),
                    React.createElement("input", { type: "text", value: userDisplayName, readOnly: true })),
                React.createElement("div", { className: styles.formGroup },
                    React.createElement("label", null, "L3 Department"),
                    React.createElement("select", { value: l3SelectedId, onChange: function (e) { return setL3SelectedId(Number(e.target.value)); } },
                        React.createElement("option", { value: "" }, "Select L3 Department"),
                        l3Options.map(function (opt) { return (React.createElement("option", { key: opt.Id, value: opt.Id }, opt.Title)); }))),
                React.createElement("div", { className: styles.formGroup },
                    React.createElement("label", null, "L4 Department"),
                    React.createElement("select", { value: l4SelectedId, onChange: function (e) { return setL4SelectedId(Number(e.target.value)); } },
                        React.createElement("option", { value: "" }, "Select L4 Department"),
                        l4Options.map(function (opt) { return (React.createElement("option", { key: opt.Id, value: opt.Id }, opt.Title)); }))))),
        React.createElement("div", { style: { overflowX: 'auto', width: '100%' } },
            React.createElement("table", { style: { width: '100%', borderCollapse: 'collapse', minWidth: '1200px' } },
                React.createElement("thead", null,
                    React.createElement("tr", { style: { backgroundColor: '#ddd' } },
                        React.createElement("th", { style: { border: '1px solid #ccc' } }),
                        React.createElement("th", { style: { border: '1px solid #ccc', color: '#0070C0' } }, "Project Name"),
                        React.createElement("th", { style: { border: '1px solid #ccc', color: '#0070C0' } }, "SubProject Name"),
                        React.createElement("th", { style: { border: '1px solid #ccc', color: '#0070C0' } }, "Cost Category"),
                        React.createElement("th", { style: { border: '1px solid #ccc', color: '#0070C0' } }, "Cost Type"),
                        React.createElement("th", { style: { border: '1px solid #ccc', color: '#0070C0' } }, "Cost Description"),
                        React.createElement("th", { style: { border: '1px solid #ccc', color: '#0070C0' } }, "Activity Category"),
                        React.createElement("th", { style: { border: '1px solid #ccc', color: '#0070C0' } }, "Activity Type"),
                        React.createElement("th", { style: { border: '1px solid #ccc', color: '#0070C0' } }, "Feature Description"),
                        React.createElement("th", { style: { border: '1px solid #ccc', color: '#0070C0' } }, "Activity Content"),
                        React.createElement("th", { style: { border: '1px solid #ccc', color: '#0070C0' } }, "Budget Yearly Plan"))),
                React.createElement("tbody", null, yearlyPlanningDate.map(function (item, index) {
                    var _a, _b, _c, _d, _e;
                    var value = item.value;
                    var projNameTitle = ((_a = projectOptions.find(function (p) { return p.Id === value.projName; })) === null || _a === void 0 ? void 0 : _a.Title) || value.projName || 'NA';
                    var subProjTitle = ((_b = subProjectOptions.find(function (s) { return s.Id === value.subprojName; })) === null || _b === void 0 ? void 0 : _b.Title) || value.subprojName || 'NA';
                    var costCategoryTitle = costCategoryOptions.includes(value.CostCategory) ? value.CostCategory : 'NA';
                    var costTypeTitle = ((_c = costTypeOptions.find(function (c) { return c.Id === value.CostType; })) === null || _c === void 0 ? void 0 : _c.Title) || value.CostType || 'NA';
                    var costDescriptionTitle = ((_d = costDescriptionOptions.find(function (c) { return c.Id === value.CostDescription; })) === null || _d === void 0 ? void 0 : _d.Title) || value.CostDescription || 'NA';
                    var activityCategoryTitle = (function () {
                        var match = activityCategories.find(function (a) { return a.Id === value.ActivityCategory; });
                        if (!value.ActivityCategory || !match)
                            return "NA";
                        return match.Title;
                    })();
                    var activityTypeTitle = (function () {
                        var match = activityTypes.find(function (a) { return a.id === value.ActivityType; });
                        if (!value.ActivityType || !match)
                            return "NA";
                        return match.label;
                    })();
                    var featureDescriptionTitle = (function () {
                        var match = featureDescriptions.find(function (f) { return f.id === value.FeatureDescription; });
                        if (!value.FeatureDescription || !match)
                            return "NA";
                        return match.label;
                    })();
                    return (React.createElement("tr", { key: index },
                        React.createElement("td", { style: { textAlign: 'center', border: '1px solid #ccc' } },
                            React.createElement("input", { type: "checkbox", checked: selectedRows.has(index), onChange: function () { return toggleRowSelection(index); } })),
                        React.createElement("td", null, projNameTitle),
                        React.createElement("td", null, subProjTitle),
                        React.createElement("td", null, costCategoryTitle),
                        React.createElement("td", null, costTypeTitle),
                        React.createElement("td", null, costDescriptionTitle),
                        React.createElement("td", null, activityCategoryTitle),
                        React.createElement("td", null, activityTypeTitle),
                        React.createElement("td", null, featureDescriptionTitle),
                        React.createElement("td", null, (_e = value.ActivityContent) !== null && _e !== void 0 ? _e : '-'),
                        React.createElement("td", null,
                            selectedCostCategory === "Outsourcing Labor" ? (renderOutsourceLabourSummary()) : selectedCostCategory === "Material" ? (renderYearlyPlanSummary()) :
                                selectedCostCategory === "Internal Labor" ? (renderinternalPlanSummary()) :
                                    selectedCostCategory === "Department" ? (renderDepartmentYearlyPlanSummary()) :
                                        null,
                            showYearlyDetailModal && selectedYearBlock && (React.createElement(YearlyDetailModal, { yearBlock: selectedYearBlock, onClose: handleModalClose, spendingLevels: spendingLevels })),
                            showViewTable && (React.createElement(Modal, { onClose: function () { return setShowViewTable(false); } },
                                React.createElement(OutsourceLabourTable, { outsourceYears: viewModeOutsourceYears, selectedOutsourceYears: [], outsourceRowData: viewModeOutsourceRowData, addOutsourceTable: function () { }, deleteSelectedOutsourceTables: function () { }, toggleOutsourceYear: function () { }, handleOutsourceMonthChange: function () { } }))),
                            showIntYearlyDetailModal && (React.createElement(Modal, { onClose: function () { return setShowIntYearlyDetailModal(false); } },
                                React.createElement(InternalLabourTable, { labourData: internalLabourData, onDataChange: handleInternalLabourDataChange, selectedYears: selectedInternalYears, toggleYearSelection: toggleInternalYearSelection, addYear: addInternalYear, deleteYears: deleteSelectedInternalYears, calculateTotalsByQuarter: calculateTotalsByQuarter, department: Department, isModal: true, onUpdate: function () {
                                        // Handle update logic, e.g., save changes and close modal
                                        handleSaveInternalLaborBudget();
                                        setShowIntYearlyDetailModal(false);
                                    }, onCancel: function () {
                                        // Just close modal without saving
                                        setShowIntYearlyDetailModal(false);
                                    } }))))));
                })),
                React.createElement("tbody", null)),
            React.createElement("div", { style: { display: 'flex', justifyContent: 'flex-end', marginTop: '10px' } },
                React.createElement("button", { style: addButtonStyle, onClick: function () {
                        if (!l3SelectedId) {
                            setErrorMessage("Please select L3 departments.");
                            setErrorPopupVisible(true);
                            return;
                        }
                        if (!l4SelectedId) {
                            setErrorMessage("Please select L4 departments.");
                            setErrorPopupVisible(true);
                            return;
                        }
                        if (activeButton === 'project') {
                            setShowPopup(true);
                        }
                        else if (activeButton === 'department') {
                            setShowDepartmentPopup(true);
                        }
                    } }, "Add"),
                React.createElement("button", { style: deleteButtonStyle, onClick: deleteSelectedRows, disabled: selectedRows.size === 0 }, "Delete"))),
        renderPopupForm(),
        renderDepartmentPopupForm(),
        renderErrorPopup()));
};
export default NewPlanningWp;
//# sourceMappingURL=NewPlanningWp.js.map