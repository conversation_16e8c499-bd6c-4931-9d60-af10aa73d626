var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
import React from "react";
var formatMoney = function (value) {
    if (value === 0)
        return ""; // Optional: don't show 0 values
    return value.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });
};
var useOutsourceLabour = function (curYear) {
    var _a = React.useState([curYear]), outsourceYears = _a[0], setOutsourceYears = _a[1];
    var _b = React.useState([]), selectedOutsourceYears = _b[0], setSelectedOutsourceYears = _b[1];
    var _c = React.useState(function () { return [
        [
            {
                category: "FTE Plan (Numbers)",
                months: Array(12).fill(""), // <-- cast here
                totals: { op: "", ea1: "", ea2: "", yearEnd: "" },
                yearSummary: { plan: 0, fte: 0 },
            },
            {
                category: "FTE Budget Plan (T-JPY)",
                months: Array(12).fill(""),
                totals: { op: "", ea1: "", ea2: "", yearEnd: "" },
                yearSummary: { plan: 0, fte: 0 },
            },
            {
                category: "FTE Budget Actual (T-JPY)",
                months: Array(12).fill(""),
                totals: { op: "", ea1: "", ea2: "", yearEnd: "" },
                yearSummary: { plan: 0, fte: 0 },
            },
        ],
    ]; }), outsourceRowData = _c[0], setOutsourceRowData = _c[1];
    // Placeholder for your actual summary calculation logic
    var calculateYearSummary = function (monthlyValues, spendingLevel, // e.g. "Labour"
    year, planValue // optional parameter if needed
    ) {
        var now = new Date();
        var currentYear = now.getFullYear();
        var curMonth = now.getMonth(); // 0 = Jan, 11 = Dec
        var total = monthlyValues.reduce(function (sum, val) { return sum + (isNaN(val) ? 0 : val); }, 0);
        var op = 0, ea1 = 0, ea2 = 0, yearEnd = 0;
        if (year === currentYear) {
            if (curMonth >= 0 && curMonth <= 2) {
                // Jan-Mar: OP = full year total
                op = total;
            }
            else if (curMonth >= 3 && curMonth <= 5) {
                // Apr-Jun: EA1 = full year total
                ea1 = total;
            }
            else if (curMonth >= 6 && curMonth <= 8) {
                // Jul-Sep: EA2 = full year total
                ea2 = total;
            }
            else if (curMonth >= 9 && curMonth <= 11) {
                // Oct-Dec: Year-End = full year total
                yearEnd = total;
            }
        }
        else {
            // For past or future years, assign OP to total as default
            op = total;
        }
        return {
            op: op,
            ea1: ea1,
            ea2: ea2,
            yearEnd: yearEnd,
            total: total,
        };
    };
    var addOutsourceTable = function () {
        var lastYear = outsourceYears[outsourceYears.length - 1];
        if (lastYear >= 2035)
            return;
        var newYear = lastYear + 1;
        var newRows = [
            "FTE Plan (Numbers)",
            "FTE Budget Plan (T-JPY)",
            "FTE Budget Actual (T-JPY)",
        ].map(function (category) {
            var months = Array(12).fill("");
            var monthNumbers = months.map(function (m) { return parseFloat(m) || 0; });
            var _a = calculateYearSummary(monthNumbers, "Outsource", newYear), op = _a.op, ea1 = _a.ea1, ea2 = _a.ea2, yearEnd = _a.yearEnd, total = _a.total;
            var conversionRates = {
                2024: 19.2,
                2025: 19.2,
                2026: 19.2,
            };
            var conversionRate = conversionRates[newYear] || 1;
            var fte = conversionRate > 0 ? total / conversionRate : 0;
            return {
                category: category,
                months: months,
                totals: {
                    op: op ? formatMoney(op) : "",
                    ea1: ea1 ? formatMoney(ea1) : "",
                    ea2: ea2 ? formatMoney(ea2) : "",
                    yearEnd: yearEnd ? formatMoney(yearEnd) : "",
                },
                yearSummary: {
                    plan: total,
                    fte: fte,
                },
            };
        });
        setOutsourceYears(function (prevYears) { return __spreadArray(__spreadArray([], prevYears, true), [newYear], false); });
        setOutsourceRowData(function (prevData) { return __spreadArray(__spreadArray([], prevData, true), [newRows], false); });
    };
    var deleteSelectedOutsourceTables = function () {
        setOutsourceYears(function (prev) { return prev.filter(function (year) { return !selectedOutsourceYears.includes(year); }); });
        setOutsourceRowData(function (prev) {
            return prev.filter(function (_, idx) {
                var year = outsourceYears[idx];
                return !selectedOutsourceYears.includes(year);
            });
        });
        setSelectedOutsourceYears([]);
    };
    var toggleOutsourceYear = function (year) {
        setSelectedOutsourceYears(function (prev) {
            return prev.includes(year) ? prev.filter(function (y) { return y !== year; }) : __spreadArray(__spreadArray([], prev, true), [year], false);
        });
    };
    // Format money as needed
    var handleOutsourceMonthChange = function (yearIdx, rowIdx, monthIdx, value) {
        setOutsourceRowData(function (prev) {
            var newData = __spreadArray([], prev, true);
            if (!newData[yearIdx] || !newData[yearIdx][rowIdx])
                return prev;
            var row = __assign({}, newData[yearIdx][rowIdx]);
            var months = __spreadArray([], (row.months || []), true);
            months[monthIdx] = value;
            var monthNumbers = months.map(function (val) { return parseFloat(val) || 0; });
            var year = (outsourceYears === null || outsourceYears === void 0 ? void 0 : outsourceYears[yearIdx]) || 0;
            var _a = calculateYearSummary(monthNumbers, "Outsource", year), op = _a.op, ea1 = _a.ea1, ea2 = _a.ea2, yearEnd = _a.yearEnd, total = _a.total;
            var conversionRates = {
                2024: 19.2,
                2025: 19.2,
                2026: 19.2,
            };
            var conversionRate = conversionRates[year] || 1;
            var fte = conversionRate > 0 ? total / conversionRate : 0;
            newData[yearIdx][rowIdx] = __assign(__assign({}, row), { months: months, totals: {
                    op: op ? formatMoney(op) : "",
                    ea1: ea1 ? formatMoney(ea1) : "",
                    ea2: ea2 ? formatMoney(ea2) : "",
                    yearEnd: yearEnd ? formatMoney(yearEnd) : "",
                }, yearSummary: {
                    plan: total,
                    fte: fte,
                } });
            return newData;
        });
    };
    React.useEffect(function () {
        console.log("✅ outsourceRowData updated:", outsourceRowData);
    }, [outsourceRowData]);
    return {
        outsourceYears: outsourceYears,
        selectedOutsourceYears: selectedOutsourceYears,
        outsourceRowData: outsourceRowData,
        addOutsourceTable: addOutsourceTable,
        deleteSelectedOutsourceTables: deleteSelectedOutsourceTables,
        toggleOutsourceYear: toggleOutsourceYear,
        handleOutsourceMonthChange: handleOutsourceMonthChange,
    };
};
export default useOutsourceLabour;
//# sourceMappingURL=useOutsourceLabour.js.map