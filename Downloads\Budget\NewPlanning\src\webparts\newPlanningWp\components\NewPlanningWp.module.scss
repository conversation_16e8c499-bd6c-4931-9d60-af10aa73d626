
.headerContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  z-index: 1100;
  height: 35px;
  border-bottom: 1px solid #ddd;
}

.logoLink {
  display: flex;
  align-items: center;
}

.logoImg {
  height: 40px;
}

.titleText {
  color: #00677f;
  font-family: CorpoS, sans-serif;
  font-size: 22px;
  font-weight: bold;
  flex: 1;
    color: #00677f;
  font-family: CorpoS, sans-serif;
  font-size: 22px;
  font-weight: bold;
  flex: 1;
  padding:20px;
  text-align: left;
  margin-top:15px;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #00677f;
  font-family: CorpoS, sans-serif;
  font-weight: bold;
}
.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profileImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.userNameLabel {
  white-space: nowrap;
}
.navbar {
  display: flex;
  position: fixed;
  top: 55px;
  left: 0;
  width: 100%;
  height: 55px;
  background-color: #0A3981;
  z-index: 1000;
  align-items: center;
  padding: 0 16px;
  overflow-x: auto;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  scrollbar-width: none;
  -ms-overflow-style: none;
  font-weight: 700;

  &::-webkit-scrollbar {
    display: none;
  }
}
.leftMenu {
  display: flex;
  align-items: center;
  overflow-x: auto;
  white-space: nowrap;

  /* hide scrollbar */
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.rightMenu {
  margin-left: auto; /* pushes logout all the way right */
  display: flex;
  align-items: center;
}
.msDropdownlabel {
  font-size: 16px;
  font-weight: bold;
  color:black;
  font-weight:700;
  margin-bottom: 6px;
  padding:5px 15px;
}


.navItem {
  display: flex;
  align-items: center;
  color: white;
  text-decoration: none;
  font-size: 15px;
  padding: 8px 12px;
  margin-right: 10px;
  border-radius: 4px;
  transition: background-color 0.2s;

  .msIcon {
    margin-right: 6px;
    font-size: 16px;
  }

  &:hover {
    background-color: white;
    color: black;
    font-weight: 800;
  }
}


.navItem {
  display: flex;
  align-items: center;
  color: white;
  text-decoration: none;
  font-size: 15px;
  padding: 8px 12px;
  margin-right: 10px;
  border-radius: 4px;
  transition: background-color 0.2s;

  .msIcon {
    margin-right: 6px;
    font-size: 16px;
  }

  &:hover {
    background-color: white;
    color: black;
    font-weight: 800;
  }
}

.extraRightBox {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px;
}
.dropdownContainer {
  position: fixed;
  top: 105px; /* below navbar */
  left: 0;
  width: 100%;
  height: 80px;  /* slightly taller to fit label + dropdown */
  background-color: #E9DFC3;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
  overflow-x: auto;

  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.dropdownWrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end; /* align labels neatly */
    position: relative;
  overflow: visible;
  z-index: 1;
}
.tableContainer {
  max-height: 400px; /* or any fixed height */
  overflow-y: auto;
  border: 1px solid #ddd;
}

table {
  border-collapse: collapse;
  width: 100%;
}

th {
  position: sticky;
  top: 0;
  background-color: #f3f2f1;
  z-index: 10;
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
  white-space: nowrap;
}

td {
  border: 1px solid #ddd;
}

.activeNavItem {
  background-color: white;
  color: #0a3981;
  font-weight: 600;
  box-shadow: inset 0 -2px 0 #0a3981;
}
.budgetForm {
  font-family: Arial, sans-serif;
  padding: 20px;
}

.buttonGroup {
  margin-bottom: 20px;

  button {
    padding: 10px 20px;
    margin-right: 10px;
    font-size: 16px;
    border: none;
    cursor: pointer;
  }
}

.active {
  background-color: black;
  color: white;
}

.inactive {
  background-color: Grey;
  color: white;
}

.formRow {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.formGroup {
  display: flex;
  flex-direction: column;

  label {
    margin-bottom: 5px;
  }

  input,
  select {
    padding: 10px;
    width: 350px;
    font-size: 14px;
     border: 1px solid #ccc;
    border-radius: 4px;
    //transition: all 0.2s ease-in-out;
    outline: none;
  }

  input[readonly] {
    background-color: #f1f1f1;
    padding: 5px;
    border: 2px solid #80808026;
  }
}
.formGroup 
input:focus,
select:focus {
  border: 2px solid #0078D4; /* Fluent UI blue */
  outline: none;             /* Remove default black outline */
  box-shadow: 0 0 4px rgba(0, 120, 212, 0.5); /* Optional soft blue glow */
  background-color: #f3f9fd; /* Optional slight blue background */
}

