import { sp } from "@pnp/sp";
import React from "react";

// === TYPES ===
interface Totals {
  op: string;
  ea1: string;
  ea2: string;
  yearEnd: string;
  total: string;
}

interface LabourRow {
  category: string;
  months: string[];
  totals: Totals;
  labourRate?: number; // Add labourRate if needed for rate calculation
}

interface LabourYearData {
  year: number;
  rows: LabourRow[];
  totalPlanHours?: number; // <-- Add this
}

// === CONSTANTS ===
const editableLabourCategories = new Set([
  "Labor Plan (hrs)",
  "Labor Actual (hrs)",
  "Labor Plan (T-JPY)",
  "Labor Actual (T-JPY)"
]);

const stickyLeft1: React.CSSProperties = {
  position: "sticky",
  whiteSpace:"noWrap",
  left: 0,
  background: "#ccc",
  zIndex: 3,
  borderRight: "1px solid #ccc",
  padding: "4px"
};
const stickyLeft2: React.CSSProperties = {
  position: "sticky",
    whiteSpace:"noWrap",
  left: 0,
  background: "#ccc",
  zIndex: 3,
  borderRight: "1px solid #ccc",
  padding: "4px"
};
const stickyThStyle: React.CSSProperties = {
  position: "sticky",
    whiteSpace:"noWrap",
  top: 0,
  background: "#ccc",
  zIndex: 2,
  borderBottom: "2px solid #999",
  padding: "4px"
};

const formatMoney = (value: number | undefined | null): string => {
  if (typeof value !== "number" || isNaN(value)) return "";
  return value.toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const parseMoney = (s: string | number | undefined): number => {
  if (s === undefined || s === null) return 0;
  if (typeof s === "number") return isFinite(s) ? s : 0;
  const cleaned = s.toString().replace(/,/g, "").trim();
  const n = parseFloat(cleaned);
  return isFinite(n) ? n : 0;
};

// === PROPS ===
interface InternalLabourTableProps {
  labourData: LabourYearData[];
  onDataChange: (updatedData: LabourYearData[]) => void;
  selectedYears: number[];
  toggleYearSelection: (year: number) => void;
  addYear: () => void;
  deleteYears: () => void;
  calculateTotalsByQuarter: (months: string[], rate: number) => Totals;
   department: string;
   isModal:boolean;
   onUpdate?: () => void;              
   // optional callback for update button
  onCancel?: () => void;  
}

const fetchLabourRate = async (department: string, year: number): Promise<number | null> => {
  try {
     console.log(`Fetching labour rate for department: "${department}", year: ${year}`);
    const items = await sp.web.lists
      .getByTitle("Labour Rate")
      .items
      .select("Title", "Year", "Department/Title")
      .expand("Department")
      .filter(`Department/Title eq '${department}' and Year eq ${year}`)
      .top(1)
      .get();

    if (items.length > 0) {
      const rate = parseFloat(items[0].Title);
      return isNaN(rate) ? null : rate;
    } else {
      console.warn(`No labour rate found for department "${department}" and year "${year}".`);
      return null;
    }
  } catch (error) {
    console.error("Error fetching labour rate:", error);
    return null;
  }
};

const updateTotalPlanHours = (data: LabourYearData[]): LabourYearData[] => {
  return data.map((yearData) => {
    const planRow = yearData.rows.find(r => r.category === "Labor Plan (hrs)");
    const total = planRow
      ? planRow.months.reduce((sum, val) => sum + parseMoney(val), 0)
      : 0;
    return { ...yearData, totalPlanHours: total };
  });
};

// === COMPONENT ===
const InternalLabourTable: React.FC<InternalLabourTableProps> = ({
  labourData,
  onDataChange,
  selectedYears,
  toggleYearSelection,
  addYear,
  deleteYears,
  calculateTotalsByQuarter,
  department,
   isModal,
  onUpdate,
  onCancel,
}) => {
const fetchedYearsRef = React.useRef<Set<number>>(new Set());
React.useEffect(() => {
  const fetchRatesForAllYears = async () => {
    const updatedData = [...labourData];
    let updated = false;

    for (let i = 0; i < updatedData.length; i++) {
      const year = updatedData[i].year;

      if (fetchedYearsRef.current.has(year)) continue;

      const rate = await fetchLabourRate(department, year);
      if (rate !== null) {
        updatedData[i].rows[0].labourRate = rate;
        updatedData[i].rows[1].labourRate = rate;

        [2, 3].forEach((budgetRowIdx) => {
          const sourceRowIdx = budgetRowIdx - 2;
          const sourceRow = updatedData[i].rows[sourceRowIdx];
          const budgetRow = updatedData[i].rows[budgetRowIdx];

          const budgetMonths = sourceRow.months.map((m) =>
            formatMoney(parseMoney(m) * rate)
          );
          const budgetTotals = calculateTotalsByQuarter(sourceRow.months, rate);

          updatedData[i].rows[budgetRowIdx] = {
            ...budgetRow,
            months: budgetMonths,
            totals: budgetTotals,
            labourRate: rate,
          };
        });

        updated = true;
        fetchedYearsRef.current.add(year);
      }
    }

    if (updated) {
      const updatedWithTotal = updateTotalPlanHours(updatedData);
      onDataChange(updatedWithTotal);
    }
  };

  const newYears = labourData
    .map((d) => d.year)
    .filter((y) => !fetchedYearsRef.current.has(y));

  if (department && newYears.length > 0) {
    fetchRatesForAllYears();
  }
}, [department, labourData.map(d => d.year).join(",")]); // 👈 this makes it stable

const handleMonthChange = (
  yearIdx: number,
  rowIdx: number,
  monthIdx: number,
  value: string
) => {
  const updated = labourData.map((yearData, y) => {
    if (y !== yearIdx) return yearData;

    // Prepare new months for the edited row (immutable)
    const editedRow = yearData.rows[rowIdx];
    const newMonths = [...editedRow.months];
    newMonths[monthIdx] = value;

    const rate = yearData.rows[0].labourRate ?? 1;

    const newRows = yearData.rows.map((row, r) => {
      if (r === rowIdx) {
        // Update the edited row with new months and totals
        const newTotals = calculateTotalsByQuarter(newMonths, r <= 1 ? 1 : rate);
        return { ...row, months: newMonths, totals: newTotals };
      }
      if ((rowIdx === 0 || rowIdx === 1) && r === rowIdx + 2) {
        // Use updated newMonths from edited row for budget rows
        const budgetMonths = newMonths.map(m => formatMoney(parseMoney(m) * rate));
        const budgetTotals = calculateTotalsByQuarter(newMonths, rate);
        return { ...row, months: budgetMonths, totals: budgetTotals, labourRate: rate };
      }
      return row;
    });

    return { ...yearData, rows: newRows };
  });

  const updatedWithTotal = updateTotalPlanHours(updated);
  onDataChange(updatedWithTotal);
};

const handleLabourRateChange = (yearIdx: number, newRateStr: string) => {
  const newRate = parseMoney(newRateStr);
  if (newRate <= 0) {
    alert("Please enter a valid labour rate greater than 0");
    return;
  }
  const updated = [...labourData];
  const yearData = updated[yearIdx];

  // Update labourRate on Plan and Actual rows
  yearData.rows[0].labourRate = newRate;
  yearData.rows[1].labourRate = newRate;

  // Recalculate budget rows (2 and 3) based on updated rate and current months
  [2, 3].forEach((budgetRowIdx) => {
    const sourceRowIdx = budgetRowIdx - 2;
    const sourceRow = yearData.rows[sourceRowIdx];
    const budgetRow = yearData.rows[budgetRowIdx];

    const budgetMonths = sourceRow.months.map((m) =>
      formatMoney(parseMoney(m) * newRate)
    );
    const budgetTotals = calculateTotalsByQuarter(sourceRow.months, newRate);

    yearData.rows[budgetRowIdx] = {
      ...budgetRow,
      months: budgetMonths,
      totals: budgetTotals,
      labourRate: newRate,
    };
  });

  const updatedWithTotal = updateTotalPlanHours(updated);
  onDataChange(updatedWithTotal);
};
  return (
    <div>
      {labourData.map((data, yearIdx) => (
        <div
          key={data.year}
          style={{
            overflowX: "auto",
            margin: "10px 0",
            border: selectedYears.includes(data.year)
              ? "2px solid blue"
              : "1px solid #ccc",
            padding: 10,
            borderRadius: 4,
          }}
        >
          <tr>
  {yearIdx !== 0 && (
    <>
      <td style={stickyLeft2} colSpan={16}>
        <label
          style={{
            cursor: "pointer",
            userSelect: "none",
            display: "flex",
            alignItems: "center",
            gap: 8,
          }}
        >
          <input
            type="checkbox"
            checked={selectedYears.includes(data.year)}
            onChange={() => toggleYearSelection(data.year)}
          />
          <strong>Select table to delete</strong>
        </label>
      </td>
    </>
  )}
</tr>

          <table
            style={{
              width: "100%",
              borderCollapse: "collapse",
              textAlign: "center",
              minWidth: 900, // To allow horizontal scrolling for many columns
            }}
          >
            <thead>
                 <tr>
                  <th style={stickyLeft1}>Year</th>
                  <th style={stickyLeft2}>Total Year Plan (hrs)</th>
                  <th style={stickyThStyle}>Total Year Plan (T-JPY)</th>
                  <th style={stickyThStyle}>Labor Rate (T-JPY)</th>
                </tr>
               <tr>
  <td style={stickyLeft1}>{data.year}</td>
  <td style={stickyLeft2}> 
    {formatMoney(data.totalPlanHours)}
  </td>
  <td> {
      formatMoney(
        parseMoney(
          data.rows.find(row => row.category === "Labor Plan (T-JPY)")?.totals.total
        )
      )
    }</td>
  <td>
    <input
      type="text"
      value={data.rows[0].labourRate ?? ""}
      onChange={(e) => handleLabourRateChange(yearIdx, e.target.value)}
      style={{ width: "80px", textAlign: "right" }}
    />
  </td>
</tr>

              <tr>
                <th style={stickyLeft1}>Year</th>
                <th style={stickyLeft2}>Labour Plan vs Actual</th>
                {[
                  "JAN",
                  "FEB",
                  "MAR",
                  "APR",
                  "MAY",
                  "JUN",
                  "JUL",
                  "AUG",
                  "SEP",
                  "OCT",
                  "NOV",
                  "DEC",
                ].map((m) => (
                  <th key={m} style={stickyThStyle}>
                    {m}
                  </th>
                ))}
                <th style={stickyThStyle}>OP</th>
                <th style={stickyThStyle}>EA1</th>
                <th style={stickyThStyle}>EA2</th>
                <th style={stickyThStyle}>Year-End</th>
              </tr>
            </thead>

            <tbody>
              {data.rows.map((row, rowIdx) => (
                <tr key={rowIdx} style={{ borderBottom: "1px solid #ddd" }}>
                  <td style={stickyLeft1}>{data.year}</td>
                  <td style={stickyLeft2}>{row.category}</td>
                  {row.months.map((val, mIdx) => (
                    <td key={mIdx}>
                      {editableLabourCategories.has(row.category) ? (
                        <input
                          value={val}
                          onChange={(e) =>
                            handleMonthChange(yearIdx, rowIdx, mIdx, e.target.value)
                          }
                          style={{ width: "80px", textAlign: "right" }}
                        />
                      ) : (
                        formatMoney(parseMoney(val))
                      )}
                    </td>
                  ))}
                  <td>{row.totals.op}</td>
                  <td>{row.totals.ea1}</td>
                  <td>{row.totals.ea2}</td>
                  <td>{row.totals.yearEnd}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ))}

      {isModal ? (
  <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '10px' }}>
    <button onClick={onUpdate} style={{ marginRight: 8 }}>Update</button>
    <button onClick={onCancel}>Cancel</button>
  </div>
) : (
  <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '10px' }}>
    <button onClick={addYear}>Add</button>
    <button onClick={deleteYears} disabled={selectedYears.length === 0}>Delete</button>
  </div>
)}

    </div>
  );
};

export default React.memo(InternalLabourTable);
