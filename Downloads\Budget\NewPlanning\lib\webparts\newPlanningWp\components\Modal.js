import React from "react";
var Modal = function (_a) {
    var onClose = _a.onClose, children = _a.children;
    return (React.createElement("div", { style: {
            position: "fixed",
            top: 0, left: 0, right: 0, bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 9999,
            padding: "20px",
        } },
        React.createElement("div", { style: {
                backgroundColor: "#fff",
                borderRadius: "8px",
                maxWidth: "95vw",
                maxHeight: "90vh",
                overflow: "hidden",
                position: "relative",
                width: "100%",
            } },
            React.createElement("button", { onClick: onClose, style: {
                    position: "absolute",
                    top: "10px",
                    right: "10px",
                    backgroundColor: "#a00",
                    color: "#fff",
                    border: "none",
                    padding: "6px 12px",
                    borderRadius: "4px",
                    cursor: "pointer",
                    zIndex: 1,
                } }, "Close"),
            React.createElement("div", { style: {
                    overflowX: "auto",
                    overflowY: "auto",
                    padding: "20px",
                    maxHeight: "calc(90vh - 60px)", // leave room for close button
                } }, children))));
};
export default Modal;
//# sourceMappingURL=Modal.js.map