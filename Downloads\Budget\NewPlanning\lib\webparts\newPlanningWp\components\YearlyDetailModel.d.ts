import React from "react";
interface YearlyEntry {
    Type: string;
    jan?: string | number;
    feb?: string | number;
    mar?: string | number;
    apr?: string | number;
    may?: string | number;
    jun?: string | number;
    jul?: string | number;
    aug?: string | number;
    sep?: string | number;
    oct?: string | number;
    nov?: string | number;
    dec?: string | number;
    op?: string | number;
    ea1?: string | number;
    ea2?: string | number;
    yearEnd?: string | number;
    initialJPY?: string | number;
    [key: string]: number | string | undefined;
}
interface YearBlock {
    key: string | number;
    value: YearlyEntry[];
}
interface YearlyDetailModalProps {
    yearBlock: YearBlock;
    onClose: () => void;
    spendingLevels: string[];
}
declare const YearlyDetailModal: React.FC<YearlyDetailModalProps>;
export default YearlyDetailModal;
//# sourceMappingURL=YearlyDetailModel.d.ts.map