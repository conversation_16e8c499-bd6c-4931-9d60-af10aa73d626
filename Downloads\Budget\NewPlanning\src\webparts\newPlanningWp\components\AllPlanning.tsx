import * as React from 'react';
import styles from './NewPlanningWp.module.scss';
import { Icon, initializeIcons } from '@fluentui/react';
import { sp } from "@pnp/sp/presets/all";

initializeIcons();
const menuItems = [
  { key: 'home', text: 'Home', icon: 'Home', href: '#home' },
  { key: 'newplanning', text: 'New Planning', icon: 'AddTo', href: '#newplanning' },
  { key: 'mgrplanning', text: 'Mgr. Planning', icon: 'People', href: '#mgrplanning' },
  { key: 'allplanning', text: 'All Planning', icon: 'ViewAll', href: '#allplanning' },
  { key: 'splapproval', text: 'SPL Approval', icon: 'CheckMark', href: '#splapproval' },
  { key: 'budgetapproval', text: 'Budget Approval', icon: 'Money', href: '#budgetapproval' },
  { key: 'report', text: 'Report', icon: 'ReportDocument', href: '#report' },
  { key: 'masterlist', text: 'Master List', icon: 'List', href: '#masterlist' },
  { key: 'administration', text: 'Administration', icon: 'Settings', href: '#administration' },
  { key: 'chatbox', text: 'ChatBox', icon: 'Chat', href: '#chatbox' },
];

const AllPlanningWp: React.FC = () => {
  

sp.setup({
  sp: {
    baseUrl: "https://corptb.sharepoint.com/sites/apac-10747/"
  }
});
// usage

const [activeKey, setActiveKey] = React.useState<string>('newplanning');

  const handleClick = (key: string):void => {
    setActiveKey(key);
  };

  const [userName, setUserName] = React.useState('Loading...');
const [profilePhoto, setProfilePhoto] = React.useState<string | null>(null);

React.useEffect(() => {
  const fetchUserInfo:() => Promise<void> = async () => {
    try {
      const currentUser = await sp.web.currentUser.get();
      const userProfile = await sp.profiles.myProperties.get();

      setUserName(currentUser.Title);

      const pictureUrlProp = userProfile.UserProfileProperties.find(
        (prop: { Key: string; }) => prop.Key === "PictureURL"
      );

      if (pictureUrlProp && pictureUrlProp.Value) {
        setProfilePhoto(pictureUrlProp.Value);
      } else {
        setProfilePhoto(null); // fallback
      }
    } catch (error) {
      console.error("Error fetching user profile info", error);
    }
  };

fetchUserInfo().catch(error => {
  console.error("Unhandled error in fetchUserInfo", error);
});

}, []);

  return (
    <>
 <div className={styles.headerContainer}>
     <a
  href="https://corptb.sharepoint.com/sites/apac-10747/SitePages/budgetController.aspx"
  className={styles.logoLink}
  aria-label="Go to Home"
>
  <img
    src="https://corptb.sharepoint.com/sites/apac-10747/SiteAssets/BudgetV1/assets/img/fuso-logo.svg"
    alt="Logo"
    className={styles.logoImg}
  />
</a>
      <span className={styles.titleText}>R&D Budget Management Database</span>
               <div className={styles.userInfo}>
        <div className={styles.avatar}>
            {profilePhoto ? (
            <img src={profilePhoto} alt="Profile" className={styles.profileImg} />
            ) : (
            <i className="bi bi-person-circle"/>
            )}
        </div>
        <label id="userName" className={styles.userNameLabel}>
            {userName}
        </label>
        </div>
    </div>
    <div className={styles.navbar}>
      <div className={styles.leftMenu}>
        {menuItems.map(item => (
            <a key={item.key} href={item.href} className={`${styles.navItem} ${activeKey === item.key ? styles.activeNavItem : ''}`}  onClick={() => handleClick(item.key)}>
            <Icon iconName={item.icon} styles={{ root: { marginRight: 6, fontSize: 16 } }} />
            {item.text}
          </a>
        ))}
      </div>

      <div className={styles.rightMenu}>
        <a href="#logout" className={styles.navItem}>
          <Icon iconName="SignOut" styles={{ root: { marginRight: 6, fontSize: 16 } }} />
          Logout
        </a>
      </div>


    </div>
      </>
  );
};

export default AllPlanningWp;
