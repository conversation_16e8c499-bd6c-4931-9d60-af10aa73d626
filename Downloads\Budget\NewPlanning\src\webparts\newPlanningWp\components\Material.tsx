import React from "react";

interface MonthTotals {
  op?: string;
  ea1?: string;
  ea2?: string;
  yearEnd?: string;
  total?: string;
}

interface Row {
  spendingLevel: string;
  months: string[];
  totals: MonthTotals;
}

interface YearData {
  year: number;
  rows: Row[];
}

interface MaterialTableProps {
  index: number;
  tblCounter: number;
  curYear: number;
  createMaterialTable: () => void;
  deleteSelectedTables: () => void;
  selectedYears: number[];
  toggleYearSelection: (year: number) => void;
  tableYears: number[];
  rowData: YearData[];
  spendingLevels: string[];
  editableLevels: Set<string>;
  handleMonthChange: (
    yearIdx: number,
    rowIdx: number,
    monthIdx: number,
    value: string
  ) => void;
  isModal:boolean;
   onUpdate?: () => void;              
   // optional callback for update button
  onCancel?: () => void;  

}

const MaterialTable: React.FC<MaterialTableProps> = ({
  index,
  tblCounter,
  curYear,
  createMaterialTable,
  deleteSelectedTables,
  selectedYears,
  toggleYearSelection,
  tableYears,
  rowData,
  spendingLevels,
  editableLevels,
  handleMonthChange,
  isModal,
  onCancel,
  onUpdate,
}) => {
  const [selectedCurrency, setSelectedCurrency] = React.useState("JPY");
  const [editingCell, setEditingCell] = React.useState<{row: number, month: number} | null>(null);


const fxRates: Record<string, number> = {
  JPY: 1,
  USD: 1/166,
  EUR: 1/150.91,
  CNY: 1/21.42,
};

const fxRate = fxRates[selectedCurrency];

// Utility to safely parse and convert numbers
const convertValue = (val?: string): string => {
  if (!val) return "";
  const num = parseFloat(val.replace(/,/g, ""));
  return isNaN(num) ? "" : (num * fxRate).toFixed(2);
};

  return (
    <div>
      {tableYears.map((year, idx) => (
        <div key={year} style={{ marginBottom: 40, padding: 20 }}>
          {idx !== 0 && (
            <input
              type="checkbox"
              checked={selectedYears.includes(year)}
              onChange={() => toggleYearSelection(year)}
            />
          )}
          <h3>Material Table - Year {year}</h3>
          <div style={{ overflowX: "auto", width: "100%" }}>
            <table
              style={{
                tableLayout: "auto",
                width: "auto",
                borderCollapse: "collapse",
              }}
            >
              <thead>
                <tr>
                  <th
                    style={{
                      padding: "8px",
                      border: "1px solid #ccc",
                      whiteSpace: "nowrap",
                    }}
                  >
                    Year
                  </th>
                <th
  style={{
    padding: "8px",
    border: "1px solid #ccc",
    textAlign: "right",
    whiteSpace: "nowrap",
  }}
              >
                Total Year Plan (
                <select
                  value={selectedCurrency}
                  onChange={(e) => setSelectedCurrency(e.target.value)}
                  style={{
                    fontWeight: "bold",
                    border: "none",
                    background: "transparent",
                    outline: "none",
                    cursor: "pointer",
                  }}
                >
                  <option value="JPY">T JPY</option>
                  <option value="USD">T USD</option>
                  <option value="EUR">T EUR</option>
                  <option value="CNY">T CNY</option>
                </select>
                )
              </th>
                </tr>
                <tr>
                  <td
                    style={{
                      padding: "8px",
                      border: "1px solid #ccc",
                      whiteSpace: "nowrap",
                    }}
                  >
                    <select defaultValue={year} style={{ width: "100%" }}>
                      {Array.from({ length: 2036 - curYear }, (_, i) => curYear + i).map(
                        (y) => (
                          <option key={y} value={y}>
                            {y}
                          </option>
                        )
                      )}
                    </select>
                  </td>
                  <td
                    style={{
                      padding: "8px",
                      border: "1px solid #ccc",
                      whiteSpace: "nowrap",
                    }}
                  >
                   {/*  {
                      rowData
                        .find((r) => r.year === year)
                        ?.rows.find((r) => r.spendingLevel === "SL4 Payment Plan")
                        ?.totals.total
                    } */}
                    <td>
  {(() => {
    const total = rowData
      .find((r) => r.year === year)
      ?.rows.reduce((sum, row) => {
        const t = parseFloat(row.totals.total?.replace(/,/g, "") || "0");
        return sum + (isNaN(t) ? 0 : t);
      }, 0);

    return total ? (total * fxRate).toFixed(2) : "";
  })()}
</td>

                  </td>
                </tr>
                <tr>
                  <th>Year</th>
                  <th>Spending Level</th>
                  {[
                    "JAN",
                    "FEB",
                    "MAR",
                    "APR",
                    "MAY",
                    "JUN",
                    "JUL",
                    "AUG",
                    "SEP",
                    "OCT",
                    "NOV",
                    "DEC",
                  ].map((month) => (
                    <th key={month}>{month}</th>
                  ))}
                  <th>OP</th>
                  <th>EA1</th>
                  <th>EA2</th>
                  <th>Year-End</th>
                </tr>
              </thead>
              <tbody>
                {spendingLevels.map((level, i) => {
                  const yearData = rowData.find((r) => r.year === year);
                  const row = yearData?.rows.find((r) => r.spendingLevel === level);

                  return (
                    <tr key={i}>
                      <td
                        style={{
                          whiteSpace: "nowrap",
                          border: "1px solid #ddd",
                          padding: "8px",
                        }}
                      >
                        <select defaultValue={year}>
                          {Array.from({ length: 2036 - curYear }, (_, i) => curYear + i).map(
                            (y) => (
                              <option key={y} value={y}>
                                {y}
                              </option>
                            )
                          )}
                        </select>
                      </td>
                      <td
                        style={{
                          whiteSpace: "nowrap",
                          border: "1px solid #ddd",
                          padding: "8px",
                        }}
                      >
                        {level}
                      </td>
                      {Array.from({ length: 12 }).map((_, monthIdx) => (
                        <td
                          key={monthIdx}
                          style={{
                            whiteSpace: "nowrap",
                            border: "1px solid #ddd",
                            padding: "8px",
                          }}
                        >
                  <input
  type="text"
  disabled={!editableLevels.has(level)}
  style={{ width: "100px" }}
  value={
    editingCell?.row === i && editingCell?.month === monthIdx
      ? row?.months[monthIdx] ?? ""          // show raw while editing
      : convertValue(row?.months[monthIdx])  // show converted otherwise
  }
  onFocus={() => {
    if (editableLevels.has(level)) {
      setEditingCell({ row: i, month: monthIdx });
    }
  }}
  onBlur={() => setEditingCell(null)}
  onChange={(e) => {
    if (editableLevels.has(level)) {
      handleMonthChange(idx, i, monthIdx, e.target.value);
    }
  }}
/>


                        </td>
                      ))}
                      <td>
                        <input
                          type="text"
                          disabled
                          style={{ width: "100px" }}
                          //value={row?.totals.op || ""}
                          value={convertValue(row?.totals.op)}

                        />
                      </td>
                      <td>
                        <input
                          type="text"
                          disabled
                          style={{ width: "100px" }}
                          value={convertValue(row?.totals.ea1)
}
                        />
                      </td>
                      <td>
                        <input
                          type="text"
                          disabled
                          style={{ width: "100px" }}
                          value={convertValue(row?.totals.ea2)
                        }
                        />
                      </td>
                      <td>
                        <input
                          type="text"
                          disabled
                          style={{ width: "100px" }}
                          value={row?.totals.yearEnd}
                        />
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      ))}

{/*   <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '10px' }}>
    <button style={{
            backgroundColor: "green",
            color: "white",
            border: "none",
            padding: "8px 16px",
            cursor: "pointer",
            marginRight: 10,
          }}  onClick={createMaterialTable}>Add</button>
    <button 
     style={{
            backgroundColor: "#d9534f",
            color: "white",
            border: "none",
            padding: "8px 16px",
            cursor: selectedYears.length === 0 ? "not-allowed" : "pointer",
            opacity: selectedYears.length === 0 ? 0.6 : 1,
          }}
    onClick={deleteSelectedTables} disabled={selectedYears.length === 0}>Delete</button>
  </div> */}
    {isModal ? (
  <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '10px' }}>
    <button onClick={onUpdate} style={{ marginRight: 8 }}>Update</button>
    <button onClick={onCancel}>Cancel</button>
  </div>
) : (
  <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '10px' }}>
    <button style={{
            backgroundColor: "green",
            color: "white",
            border: "none",
            padding: "8px 16px",
            cursor: "pointer",
            marginRight: 10,
          }}  onClick={createMaterialTable}>Add</button>
    <button 
     style={{
            backgroundColor: "#d9534f",
            color: "white",
            border: "none",
            padding: "8px 16px",
            cursor: selectedYears.length === 0 ? "not-allowed" : "pointer",
            opacity: selectedYears.length === 0 ? 0.6 : 1,
          }}
    onClick={deleteSelectedTables} disabled={selectedYears.length === 0}>Delete</button>
  </div>
)}
    </div>
  );
};

export default MaterialTable;
