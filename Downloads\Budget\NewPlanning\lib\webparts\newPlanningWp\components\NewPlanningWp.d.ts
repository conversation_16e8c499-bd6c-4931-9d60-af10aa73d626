import * as React from 'react';
import type { INewPlanningWpProps } from './INewPlanningWpProps';
import "@pnp/sp/webs";
import "@pnp/sp/lists";
import "@pnp/sp/items";
interface Totals {
    op: string;
    ea1: string;
    ea2: string;
    yearEnd: string;
    total: string;
}
export interface LabourYearData {
    year: number;
    rows: LabourRow[];
}
export interface LabourRow {
    category: string;
    months: string[];
    totals: Totals;
    labourRate?: number;
    year: number;
}
export declare const internalLabourCategories: string[];
declare const NewPlanningWp: React.FC<INewPlanningWpProps>;
export default NewPlanningWp;
//# sourceMappingURL=NewPlanningWp.d.ts.map