import React from "react";
export type RowData = {
  category: string;
  months: string[];
  totals: {
    op: string;
    ea1: string;
    ea2: string;
    yearEnd: string;
  };
  yearSummary?: {
    plan: number;
    fte: number;
  };
};


type OutsourceLabourTableProps = {
  outsourceYears: number[];
  selectedOutsourceYears: number[];
  outsourceRowData: RowData[][];
  addOutsourceTable: () => void;
  deleteSelectedOutsourceTables: () => void;
  toggleOutsourceYear: (year: number) => void;
  handleOutsourceMonthChange: (
    yearIdx: number,
    rowIdx: number,
    monthIdx: number,
    value: string
  ) => void;
};

const OutsourceLabourTable: React.FC<OutsourceLabourTableProps> = ({
 outsourceYears,
  selectedOutsourceYears,
  outsourceRowData,
  addOutsourceTable,
  deleteSelectedOutsourceTables,
  toggleOutsourceYear,
  handleOutsourceMonthChange
}) => {

  // Simple inline styles for headers and sticky columns
  const thStyle: React.CSSProperties = {
    border: "1px solid #ccc",
    whiteSpace: "nowrap",
    textAlign: "center",
    backgroundColor: "#f0f0f0",
  };
  const stickyThStyle: React.CSSProperties = {
    ...thStyle,
    position: "sticky",
    left: 0,
    backgroundColor: "#f0f0f0",
    zIndex: 2,
  };
  const stickyTdStyle: React.CSSProperties = {
    position: "sticky",
    left: 0,
    backgroundColor: "#fff",
    zIndex: 1,
  };
  const stickyLeft1: React.CSSProperties = {
    ...stickyThStyle,
    left: 0,
    zIndex: 3,
  };
  const stickyLeft2: React.CSSProperties = {
    ...stickyThStyle,
    left: 45,
    zIndex: 3,
  };

  return (
    <div>
      {outsourceYears.map((year, idx) => (
        <div
          key={year}
          style={{ marginBottom: 40, padding: 20, border: "1px solid #ccc" }}
        >
          <div style={{ overflowX: "auto", marginTop: 10 }}>
            <table style={{ borderCollapse: "collapse", width: "100%" }}>
              <thead>
                {idx !== 0 && (
                  <tr>
                    <th style={stickyLeft1}>
                      <input
                        type="checkbox"
                        id={`chkTable_${year}`}
                        className="deleteTable"
                        checked={selectedOutsourceYears.includes(year)}
                        onChange={() => toggleOutsourceYear(year)}
                      />
                    </th>
                    <th style={stickyLeft2}>Select the checkbox to delete entire table</th>
                  </tr>
                )}
                <tr>
                  <th style={stickyLeft1}>Year</th>
                  <th style={stickyLeft2}>Total Year Plan (T-JPY)</th>
                  <th style={stickyThStyle}>Total Year FTE (Numbers)</th>
                </tr>

                {/* Summary row */}
                <tr>
                  <td style={stickyLeft1}>{year}</td>
                  <td style={stickyLeft2}>
                    {outsourceRowData[idx]?.find(r => r.category === "FTE Budget Plan (T-JPY)")?.yearSummary?.plan.toFixed(2) || "0"}
                  </td>
                  <td style={stickyThStyle}>
                    {outsourceRowData[idx]?.find(r => r.category === "FTE Budget Plan (T-JPY)")?.yearSummary?.fte.toFixed(2) || "0"}
                  </td>
                </tr>

                <tr>
                  <th style={stickyLeft1}>Year</th>
                  <th style={stickyLeft2}>FTE Plan vs. Actual</th>
                  {["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC"].map(m => (
                    <th key={m} style={stickyThStyle}>{m}</th>
                  ))}
                  <th style={stickyThStyle}>OP</th>
                  <th style={stickyThStyle}>EA1</th>
                  <th style={stickyThStyle}>EA2</th>
                  <th style={stickyThStyle}>Year-End</th>
                </tr>
              </thead>
              <tbody>
                {["FTE Plan (Numbers)", "FTE Budget Plan (T-JPY)", "FTE Budget Actual (T-JPY)"].map((category, catIdx) => (
                  <tr key={catIdx}>
                    <td style={stickyTdStyle}>{year}</td>
                    <td style={{ ...stickyTdStyle, left: 45 }}>
                      <input type="text" value={category} disabled style={{ border: "none", background: "transparent", width: "100%" }} />
                    </td>
                    {Array.from({ length: 12 }).map((_, monthIdx) => (
                      <td key={monthIdx}>
                        <input
                          type="text"
                          value={outsourceRowData[idx]?.[catIdx]?.months[monthIdx] || ""}
                          onChange={e => handleOutsourceMonthChange(idx, catIdx, monthIdx, e.target.value)}
                        />
                      </td>
                    ))}
                    <td>
                      <input
                        type="text"
                        disabled
                        value={outsourceRowData[idx]?.[catIdx]?.totals.op || ""}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        disabled
                        value={outsourceRowData[idx]?.[catIdx]?.totals.ea1 || ""}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        disabled
                        value={outsourceRowData[idx]?.[catIdx]?.totals.ea2 || ""}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        disabled
                        value={outsourceRowData[idx]?.[catIdx]?.totals.yearEnd || ""}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ))}

      <div style={{ textAlign: "right", marginTop: 20 }}>
        <button onClick={addOutsourceTable} style={{ marginRight: 10, padding: "6px 12px", backgroundColor: "#0078d4", color: "#fff" }}>
          Add
        </button>
        <button
          onClick={deleteSelectedOutsourceTables}
          disabled={selectedOutsourceYears.length === 0}
          style={{
            padding: "6px 12px",
            backgroundColor: selectedOutsourceYears.length === 0 ? "#aaa" : "#a80000",
            color: "#fff",
            cursor: selectedOutsourceYears.length === 0 ? "not-allowed" : "pointer",
          }}
        >
          Delete
        </button>
      </div>
    </div>
  );
};

export default OutsourceLabourTable;
