import React from "react";
var DepartmentTable = function (_a) {
    var index = _a.index, tblCounter = _a.tblCounter, curYear = _a.curYear, createDepartmentTable = _a.createDepartmentTable, deleteSelectedTables = _a.deleteSelectedTables, selectedYears = _a.selectedYears, toggleYearSelection = _a.toggleYearSelection, tableYears = _a.tableYears, rowData = _a.rowData, spendingLevels = _a.spendingLevels, editableLevels = _a.editableLevels, handleMonthChange = _a.handleMonthChange;
    return (React.createElement("div", null,
        tableYears.map(function (year, idx) {
            var _a, _b;
            return (React.createElement("div", { key: year, style: { marginBottom: 40, padding: 20 } },
                idx !== 0 && (React.createElement("input", { type: "checkbox", checked: selectedYears.includes(year), onChange: function () { return toggleYearSelection(year); } })),
                React.createElement("h3", null,
                    "Department Table - Year ",
                    year),
                React.createElement("div", { style: { overflowX: "auto", width: "100%" } },
                    React.createElement("table", { style: {
                            tableLayout: "auto",
                            width: "auto",
                            borderCollapse: "collapse",
                        } },
                        React.createElement("thead", null,
                            React.createElement("tr", null,
                                React.createElement("th", { style: {
                                        padding: "8px",
                                        border: "1px solid #ccc",
                                        whiteSpace: "nowrap",
                                    } }, "Year"),
                                React.createElement("th", { style: {
                                        padding: "8px",
                                        border: "1px solid #ccc",
                                        textAlign: "right",
                                        whiteSpace: "nowrap",
                                    } }, "Total Year Plan")),
                            React.createElement("tr", null,
                                React.createElement("td", { style: {
                                        padding: "8px",
                                        border: "1px solid #ccc",
                                        whiteSpace: "nowrap",
                                    } },
                                    React.createElement("select", { defaultValue: year, style: { width: "100%" } }, Array.from({ length: 2036 - curYear }, function (_, i) { return curYear + i; }).map(function (y) { return (React.createElement("option", { key: y, value: y }, y)); }))),
                                React.createElement("td", { style: {
                                        padding: "8px",
                                        border: "1px solid #ccc",
                                        whiteSpace: "nowrap",
                                    } }, (_b = (_a = rowData
                                    .find(function (r) { return r.year === year; })) === null || _a === void 0 ? void 0 : _a.rows.find(function (r) { return r.spendingLevel === "SL4 Payment Plan"; })) === null || _b === void 0 ? void 0 : _b.totals.total)),
                            React.createElement("tr", null,
                                React.createElement("th", null, "Year"),
                                React.createElement("th", null, "Spending Level"),
                                [
                                    "JAN",
                                    "FEB",
                                    "MAR",
                                    "APR",
                                    "MAY",
                                    "JUN",
                                    "JUL",
                                    "AUG",
                                    "SEP",
                                    "OCT",
                                    "NOV",
                                    "DEC",
                                ].map(function (month) { return (React.createElement("th", { key: month }, month)); }),
                                React.createElement("th", null, "OP"),
                                React.createElement("th", null, "EA1"),
                                React.createElement("th", null, "EA2"),
                                React.createElement("th", null, "Year-End"))),
                        React.createElement("tbody", null, spendingLevels.map(function (level, i) {
                            var yearData = rowData.find(function (r) { return r.year === year; });
                            var row = yearData === null || yearData === void 0 ? void 0 : yearData.rows.find(function (r) { return r.spendingLevel === level; });
                            return (React.createElement("tr", { key: i },
                                React.createElement("td", { style: {
                                        whiteSpace: "nowrap",
                                        border: "1px solid #ddd",
                                        padding: "8px",
                                    } },
                                    React.createElement("select", { defaultValue: year }, Array.from({ length: 2036 - curYear }, function (_, i) { return curYear + i; }).map(function (y) { return (React.createElement("option", { key: y, value: y }, y)); }))),
                                React.createElement("td", { style: {
                                        whiteSpace: "nowrap",
                                        border: "1px solid #ddd",
                                        padding: "8px",
                                    } }, level),
                                Array.from({ length: 12 }).map(function (_, monthIdx) {
                                    var _a;
                                    return (React.createElement("td", { key: monthIdx, style: {
                                            whiteSpace: "nowrap",
                                            border: "1px solid #ddd",
                                            padding: "8px",
                                        } },
                                        React.createElement("input", { type: "text", disabled: !editableLevels.has(level), style: { width: "100px" }, value: (_a = row === null || row === void 0 ? void 0 : row.months[monthIdx]) !== null && _a !== void 0 ? _a : "", onChange: function (e) {
                                                return handleMonthChange(idx, i, monthIdx, e.target.value);
                                            } })));
                                }),
                                React.createElement("td", null,
                                    React.createElement("input", { type: "text", disabled: true, style: { width: "100px" }, value: (row === null || row === void 0 ? void 0 : row.totals.op) || "" })),
                                React.createElement("td", null,
                                    React.createElement("input", { type: "text", disabled: true, style: { width: "100px" }, value: (row === null || row === void 0 ? void 0 : row.totals.ea1) || "" })),
                                React.createElement("td", null,
                                    React.createElement("input", { type: "text", disabled: true, style: { width: "100px" }, value: (row === null || row === void 0 ? void 0 : row.totals.ea2) || "" })),
                                React.createElement("td", null,
                                    React.createElement("input", { type: "text", disabled: true, style: { width: "100px" }, value: row === null || row === void 0 ? void 0 : row.totals.yearEnd }))));
                        }))))));
        }),
        React.createElement("div", { style: { textAlign: "right", marginTop: 20 } },
            React.createElement("button", { onClick: createDepartmentTable, style: {
                    backgroundColor: "green",
                    color: "white",
                    border: "none",
                    padding: "8px 16px",
                    cursor: "pointer",
                    marginRight: 10,
                } }, "Add"),
            React.createElement("button", { onClick: deleteSelectedTables, style: {
                    backgroundColor: "#d9534f",
                    color: "white",
                    border: "none",
                    padding: "8px 16px",
                    cursor: selectedYears.length === 0 ? "not-allowed" : "pointer",
                    opacity: selectedYears.length === 0 ? 0.6 : 1,
                }, disabled: selectedYears.length === 0 }, "Delete"))));
};
export default DepartmentTable;
//# sourceMappingURL=DepartmentTable.js.map