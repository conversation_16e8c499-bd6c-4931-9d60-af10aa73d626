import React from "react";
var MaterialTable = function (_a) {
    var index = _a.index, tblCounter = _a.tblCounter, curYear = _a.curYear, createMaterialTable = _a.createMaterialTable, deleteSelectedTables = _a.deleteSelectedTables, selectedYears = _a.selectedYears, toggleYearSelection = _a.toggleYearSelection, tableYears = _a.tableYears, rowData = _a.rowData, spendingLevels = _a.spendingLevels, editableLevels = _a.editableLevels, handleMonthChange = _a.handleMonthChange, isModal = _a.isModal, onCancel = _a.onCancel, onUpdate = _a.onUpdate;
    var _b = React.useState("JPY"), selectedCurrency = _b[0], setSelectedCurrency = _b[1];
    var _c = React.useState(null), editingCell = _c[0], setEditingCell = _c[1];
    var fxRates = {
        JPY: 1,
        USD: 1 / 166,
        EUR: 1 / 150.91,
        CNY: 1 / 21.42,
    };
    var fxRate = fxRates[selectedCurrency];
    // Utility to safely parse and convert numbers
    var convertValue = function (val) {
        if (!val)
            return "";
        var num = parseFloat(val.replace(/,/g, ""));
        return isNaN(num) ? "" : (num * fxRate).toFixed(2);
    };
    return (React.createElement("div", null,
        tableYears.map(function (year, idx) { return (React.createElement("div", { key: year, style: { marginBottom: 40, padding: 20 } },
            idx !== 0 && (React.createElement("input", { type: "checkbox", checked: selectedYears.includes(year), onChange: function () { return toggleYearSelection(year); } })),
            React.createElement("h3", null,
                "Material Table - Year ",
                year),
            React.createElement("div", { style: { overflowX: "auto", width: "100%" } },
                React.createElement("table", { style: {
                        tableLayout: "auto",
                        width: "auto",
                        borderCollapse: "collapse",
                    } },
                    React.createElement("thead", null,
                        React.createElement("tr", null,
                            React.createElement("th", { style: {
                                    padding: "8px",
                                    border: "1px solid #ccc",
                                    whiteSpace: "nowrap",
                                } }, "Year"),
                            React.createElement("th", { style: {
                                    padding: "8px",
                                    border: "1px solid #ccc",
                                    textAlign: "right",
                                    whiteSpace: "nowrap",
                                } },
                                "Total Year Plan (",
                                React.createElement("select", { value: selectedCurrency, onChange: function (e) { return setSelectedCurrency(e.target.value); }, style: {
                                        fontWeight: "bold",
                                        border: "none",
                                        background: "transparent",
                                        outline: "none",
                                        cursor: "pointer",
                                    } },
                                    React.createElement("option", { value: "JPY" }, "T JPY"),
                                    React.createElement("option", { value: "USD" }, "T USD"),
                                    React.createElement("option", { value: "EUR" }, "T EUR"),
                                    React.createElement("option", { value: "CNY" }, "T CNY")),
                                ")")),
                        React.createElement("tr", null,
                            React.createElement("td", { style: {
                                    padding: "8px",
                                    border: "1px solid #ccc",
                                    whiteSpace: "nowrap",
                                } },
                                React.createElement("select", { defaultValue: year, style: { width: "100%" } }, Array.from({ length: 2036 - curYear }, function (_, i) { return curYear + i; }).map(function (y) { return (React.createElement("option", { key: y, value: y }, y)); }))),
                            React.createElement("td", { style: {
                                    padding: "8px",
                                    border: "1px solid #ccc",
                                    whiteSpace: "nowrap",
                                } },
                                React.createElement("td", null, (function () {
                                    var _a;
                                    var total = (_a = rowData
                                        .find(function (r) { return r.year === year; })) === null || _a === void 0 ? void 0 : _a.rows.reduce(function (sum, row) {
                                        var _a;
                                        var t = parseFloat(((_a = row.totals.total) === null || _a === void 0 ? void 0 : _a.replace(/,/g, "")) || "0");
                                        return sum + (isNaN(t) ? 0 : t);
                                    }, 0);
                                    return total ? (total * fxRate).toFixed(2) : "";
                                })()))),
                        React.createElement("tr", null,
                            React.createElement("th", null, "Year"),
                            React.createElement("th", null, "Spending Level"),
                            [
                                "JAN",
                                "FEB",
                                "MAR",
                                "APR",
                                "MAY",
                                "JUN",
                                "JUL",
                                "AUG",
                                "SEP",
                                "OCT",
                                "NOV",
                                "DEC",
                            ].map(function (month) { return (React.createElement("th", { key: month }, month)); }),
                            React.createElement("th", null, "OP"),
                            React.createElement("th", null, "EA1"),
                            React.createElement("th", null, "EA2"),
                            React.createElement("th", null, "Year-End"))),
                    React.createElement("tbody", null, spendingLevels.map(function (level, i) {
                        var yearData = rowData.find(function (r) { return r.year === year; });
                        var row = yearData === null || yearData === void 0 ? void 0 : yearData.rows.find(function (r) { return r.spendingLevel === level; });
                        return (React.createElement("tr", { key: i },
                            React.createElement("td", { style: {
                                    whiteSpace: "nowrap",
                                    border: "1px solid #ddd",
                                    padding: "8px",
                                } },
                                React.createElement("select", { defaultValue: year }, Array.from({ length: 2036 - curYear }, function (_, i) { return curYear + i; }).map(function (y) { return (React.createElement("option", { key: y, value: y }, y)); }))),
                            React.createElement("td", { style: {
                                    whiteSpace: "nowrap",
                                    border: "1px solid #ddd",
                                    padding: "8px",
                                } }, level),
                            Array.from({ length: 12 }).map(function (_, monthIdx) {
                                var _a;
                                return (React.createElement("td", { key: monthIdx, style: {
                                        whiteSpace: "nowrap",
                                        border: "1px solid #ddd",
                                        padding: "8px",
                                    } },
                                    React.createElement("input", { type: "text", disabled: !editableLevels.has(level), style: { width: "100px" }, value: (editingCell === null || editingCell === void 0 ? void 0 : editingCell.row) === i && (editingCell === null || editingCell === void 0 ? void 0 : editingCell.month) === monthIdx
                                            ? (_a = row === null || row === void 0 ? void 0 : row.months[monthIdx]) !== null && _a !== void 0 ? _a : "" // show raw while editing
                                            : convertValue(row === null || row === void 0 ? void 0 : row.months[monthIdx]) // show converted otherwise
                                        , onFocus: function () {
                                            if (editableLevels.has(level)) {
                                                setEditingCell({ row: i, month: monthIdx });
                                            }
                                        }, onBlur: function () { return setEditingCell(null); }, onChange: function (e) {
                                            if (editableLevels.has(level)) {
                                                handleMonthChange(idx, i, monthIdx, e.target.value);
                                            }
                                        } })));
                            }),
                            React.createElement("td", null,
                                React.createElement("input", { type: "text", disabled: true, style: { width: "100px" }, 
                                    //value={row?.totals.op || ""}
                                    value: convertValue(row === null || row === void 0 ? void 0 : row.totals.op) })),
                            React.createElement("td", null,
                                React.createElement("input", { type: "text", disabled: true, style: { width: "100px" }, value: convertValue(row === null || row === void 0 ? void 0 : row.totals.ea1) })),
                            React.createElement("td", null,
                                React.createElement("input", { type: "text", disabled: true, style: { width: "100px" }, value: convertValue(row === null || row === void 0 ? void 0 : row.totals.ea2) })),
                            React.createElement("td", null,
                                React.createElement("input", { type: "text", disabled: true, style: { width: "100px" }, value: row === null || row === void 0 ? void 0 : row.totals.yearEnd }))));
                    })))))); }),
        isModal ? (React.createElement("div", { style: { display: 'flex', justifyContent: 'flex-end', marginTop: '10px' } },
            React.createElement("button", { onClick: onUpdate, style: { marginRight: 8 } }, "Update"),
            React.createElement("button", { onClick: onCancel }, "Cancel"))) : (React.createElement("div", { style: { display: 'flex', justifyContent: 'flex-end', marginTop: '10px' } },
            React.createElement("button", { style: {
                    backgroundColor: "green",
                    color: "white",
                    border: "none",
                    padding: "8px 16px",
                    cursor: "pointer",
                    marginRight: 10,
                }, onClick: createMaterialTable }, "Add"),
            React.createElement("button", { style: {
                    backgroundColor: "#d9534f",
                    color: "white",
                    border: "none",
                    padding: "8px 16px",
                    cursor: selectedYears.length === 0 ? "not-allowed" : "pointer",
                    opacity: selectedYears.length === 0 ? 0.6 : 1,
                }, onClick: deleteSelectedTables, disabled: selectedYears.length === 0 }, "Delete")))));
};
export default MaterialTable;
//# sourceMappingURL=Material.js.map